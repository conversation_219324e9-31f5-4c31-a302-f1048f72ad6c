import request from '@/utils/request'
import microapp from './modules/microapp'
import cicd from './modules/cicd'
import kubernetes from './modules/kubernetes'
import user from './modules/user'
import workflow from './modules/workflow'
import system from './modules/system'
import * as cmdb from './modules/cmdb'
import * as sql from './modules/sql'

const api = Object.assign(
    {},
    microapp,
    cicd,
    kubernetes,
    user,
    workflow,
    system,
    sql,
    request
)

export default api

// 导出模块用于TypeScript类型支持
export { microapp, cicd, kubernetes, user, cmdb, workflow, system, sql }

// API模块入口文件

// 导入各个API模块，避免重复导出相同名称
import * as cmdbApi from './modules/cmdb'
import * as kubernetesApi from './modules/kubernetes'
import * as microappApi from './modules/microapp'
import * as userApi from './modules/user'
import * as workflowApi from './modules/workflow'
import * as systemApi from './modules/system'
import * as sqlApi from './modules/sql'
// 重新导出，避免命名冲突
export { cmdbApi, kubernetesApi, microappApi, userApi, workflowApi, systemApi, sqlApi }

// 导出具体的API服务
export {
    // CMDB相关API
    languageApi,
    regionApi,
    dataCenterApi,
    productApi,
    environmentApi,
    projectApi,
    pipelineApi,
    gitApi,
    harborApi,
} from './modules/cmdb'

// // Kubernetes相关API - 导出默认对象
// export { default as kubernetesClusterApi } from './modules/kubernetes'

// // MicroApp相关API - 导出默认对象
// export { default as microAppServiceApi } from './modules/microapp'

// export { default as workflowApi } from './modules/workflow'

// // 用户相关API
// export { default as userService } from './modules/user'

// 其他API模块可以在这里导出
// export * from './modules/cicd'

// export * from './modules/assets' 