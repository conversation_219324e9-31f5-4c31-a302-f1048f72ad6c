package engines

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/devops-microservices/sql-service/models"
	_ "github.com/go-sql-driver/mysql"
)

// MySQLEngine MySQL数据库引擎
type MySQLEngine struct {
	*BaseEngine
}

// NewMySQLEngine 创建MySQL引擎
func NewMySQLEngine(instance *models.DBInstance) *MySQLEngine {
	return &MySQLEngine{
		BaseEngine: NewBaseEngine(instance),
	}
}

// GetConnection 获取数据库连接
func (e *MySQLEngine) GetConnection(dbName string) (*sql.DB, error) {
	if e.Connection != nil {
		return e.Connection, nil
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		e.Instance.User, e.Instance.Password, e.Instance.Host, e.Instance.Port, dbName, e.Instance.Charset)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, err
	}

	if err := db.Ping(); err != nil {
		return nil, err
	}

	e.Connection = db
	return db, nil
}

// TestConnection 测试连接
func (e *MySQLEngine) TestConnection() error {
	db, err := e.GetConnection(e.Instance.DBName)
	if err != nil {
		return err
	}
	return db.Ping()
}

// Close 关闭连接
func (e *MySQLEngine) Close() error {
	if e.Connection != nil {
		return e.Connection.Close()
	}
	return nil
}

// Query 执行查询
func (e *MySQLEngine) Query(dbName, sql string, limitNum int, schemaName, tbName string, maxExecutionTime int) (*QueryResult, error) {
	db, err := e.GetConnection(dbName)
	if err != nil {
		return nil, err
	}

	// 添加LIMIT限制
	if limitNum > 0 && !strings.Contains(strings.ToUpper(sql), "LIMIT") {
		sql = BuildLimitSQL(sql, limitNum, "mysql")
	}

	start := time.Now()
	rows, err := db.Query(sql)
	if err != nil {
		return &QueryResult{
			Error:     FormatError(err),
			QueryTime: time.Since(start),
		}, nil
	}
	defer rows.Close()

	data, columns, err := ConvertRowsToInterface(rows)
	if err != nil {
		return &QueryResult{
			Error:     FormatError(err),
			QueryTime: time.Since(start),
		}, nil
	}

	return &QueryResult{
		Columns:             columns,
		Rows:                data,
		AffectedRows:        int64(len(data)),
		QueryTime:           time.Since(start),
		SecondsBehindMaster: e.GetSecondsBehindMaster(),
	}, nil
}

// QueryCheck 查询检查
func (e *MySQLEngine) QueryCheck(dbName, sql string) map[string]interface{} {
	result := map[string]interface{}{
		"bad_query":    false,
		"has_star":     false,
		"filtered_sql": sql,
		"msg":          "",
	}

	// 检查是否包含SELECT *
	if strings.Contains(strings.ToUpper(sql), "SELECT *") {
		result["has_star"] = true
		result["msg"] = "查询语句包含SELECT *，可能影响性能"
	}

	// 检查危险操作
	dangerousKeywords := []string{"DROP", "DELETE", "TRUNCATE", "ALTER"}
	upperSQL := strings.ToUpper(sql)
	for _, keyword := range dangerousKeywords {
		if strings.Contains(upperSQL, keyword) {
			result["bad_query"] = true
			result["msg"] = fmt.Sprintf("查询语句包含危险操作: %s", keyword)
			break
		}
	}

	return result
}

// FilterSQL 过滤SQL语句
func (e *MySQLEngine) FilterSQL(sql string, limitNum int) string {
	return BuildLimitSQL(sql, limitNum, "mysql")
}

// GetAllDatabases 获取所有数据库
func (e *MySQLEngine) GetAllDatabases() (*QueryResult, error) {
	return e.Query("", "SHOW DATABASES", 0, "", "", 0)
}

// GetAllSchemas 获取所有模式（MySQL中等同于数据库）
func (e *MySQLEngine) GetAllSchemas(dbName string) (*QueryResult, error) {
	return e.Query(dbName, "SHOW DATABASES", 0, "", "", 0)
}

// GetAllTables 获取所有表
func (e *MySQLEngine) GetAllTables(dbName, schemaName string) (*QueryResult, error) {
	sql := "SHOW TABLES"
	return e.Query(dbName, sql, 0, "", "", 0)
}

// GetAllColumnsByTable 获取表的所有列
func (e *MySQLEngine) GetAllColumnsByTable(dbName, tbName, schemaName string) (*QueryResult, error) {
	sql := fmt.Sprintf("SHOW COLUMNS FROM `%s`", tbName)
	return e.Query(dbName, sql, 0, "", "", 0)
}

// GetTableMetaData 获取表元数据
func (e *MySQLEngine) GetTableMetaData(dbName, tbName string) map[string]interface{} {
	sql := fmt.Sprintf("SELECT table_name, engine, table_collation, table_rows FROM information_schema.tables WHERE table_schema='%s' AND table_name='%s'", dbName, tbName)
	result, err := e.Query(dbName, sql, 0, "", "", 0)
	if err != nil || len(result.Rows) == 0 {
		return map[string]interface{}{"rows": []interface{}{tbName, "", "", 0}}
	}
	return map[string]interface{}{"rows": result.Rows[0]}
}

// GetTableDescData 获取表结构数据
func (e *MySQLEngine) GetTableDescData(dbName, tbName, schemaName string) map[string]interface{} {
	result, err := e.GetAllColumnsByTable(dbName, tbName, schemaName)
	if err != nil {
		return map[string]interface{}{"error": err.Error()}
	}
	return map[string]interface{}{
		"column_list": result.Columns,
		"rows":        result.Rows,
	}
}

// GetTableIndexData 获取表索引数据
func (e *MySQLEngine) GetTableIndexData(dbName, tbName string) map[string]interface{} {
	sql := fmt.Sprintf("SHOW INDEX FROM `%s`", tbName)
	result, err := e.Query(dbName, sql, 0, "", "", 0)
	if err != nil {
		return map[string]interface{}{"error": err.Error()}
	}
	return map[string]interface{}{
		"column_list": result.Columns,
		"rows":        result.Rows,
	}
}

// GetTableCreate 获取建表语句
func (e *MySQLEngine) GetTableCreate(dbName, tbName, schemaName string) (*QueryResult, error) {
	sql := fmt.Sprintf("SHOW CREATE TABLE `%s`", tbName)
	return e.Query(dbName, sql, 0, "", "", 0)
}

// DescribeTable 描述表结构
func (e *MySQLEngine) DescribeTable(dbName, tbName, schemaName string) (*QueryResult, error) {
	sql := fmt.Sprintf("DESCRIBE `%s`", tbName)
	return e.Query(dbName, sql, 0, "", "", 0)
}

// ExecuteCheck 执行检查
func (e *MySQLEngine) ExecuteCheck(dbName, sql string) (*ReviewSet, error) {
	// TODO: 实现MySQL的SQL检查逻辑
	// 这里可以集成go-inception或其他SQL审核工具
	
	reviewSet := &ReviewSet{
		FullSQL:      sql,
		IsExecute:    false,
		Checked:      &[]bool{true}[0],
		WarningCount: 0,
		ErrorCount:   0,
		IsCritical:   false,
		SyntaxType:   ParseSQLType(sql),
		Rows: []ReviewResult{
			{
				ID:           1,
				Stage:        "CHECKED",
				ErrLevel:     0,
				StageStatus:  "Audit completed",
				ErrorMessage: "",
				SQL:          sql,
				AffectedRows: 0,
				Sequence:     "1",
				BackupDBName: "",
				ExecuteTime:  "0",
				SqlSha1:      "",
				BackupTime:   "",
			},
		},
		AffectedRows: 0,
	}

	return reviewSet, nil
}

// Execute 执行SQL
func (e *MySQLEngine) Execute(dbName, sql string) (*ReviewSet, error) {
	// TODO: 实现MySQL的SQL执行逻辑
	return e.ExecuteCheck(dbName, sql)
}

// GetRollback 获取回滚语句
func (e *MySQLEngine) GetRollback(workflow *models.SqlWorkflow) ([][]string, error) {
	// TODO: 实现MySQL的回滚语句生成逻辑
	return [][]string{}, nil
}

// QueryMasking 查询脱敏
func (e *MySQLEngine) QueryMasking(dbName, sql string, queryResult *QueryResult) (*QueryResult, error) {
	// TODO: 实现MySQL的数据脱敏逻辑
	return queryResult, nil
}

// GetSecondsBehindMaster 获取主从延迟
func (e *MySQLEngine) GetSecondsBehindMaster() int {
	if e.Instance.Type != "slave" {
		return 0
	}

	result, err := e.Query("", "SHOW SLAVE STATUS", 0, "", "", 0)
	if err != nil || len(result.Rows) == 0 {
		return 0
	}

	// TODO: 解析SHOW SLAVE STATUS结果获取Seconds_Behind_Master
	return 0
}

// KillConnection 终止连接
func (e *MySQLEngine) KillConnection(threadID int) error {
	db, err := e.GetConnection("")
	if err != nil {
		return err
	}

	sql := fmt.Sprintf("KILL %d", threadID)
	_, err = db.Exec(sql)
	return err
}
