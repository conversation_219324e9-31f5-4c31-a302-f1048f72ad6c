# SQL Service

DevOps微服务平台的SQL管理服务，负责数据库实例管理、SQL查询、工作流管理等功能。

## 功能特性

- **数据库实例管理**: 支持MySQL、PostgreSQL、MongoDB等多种数据库类型
- **SQL查询**: 在线SQL查询，支持查询结果缓存和下载
- **SQL工作流**: SQL上线工单管理，支持审核、执行、回滚
- **查询日志**: 记录所有SQL查询操作，支持审计
- **数据脱敏**: 敏感数据脱敏处理
- **查询权限**: 用户数据库访问权限管理
- **任务调度**: 支持定时SQL执行和连接管理
- **SQL优化**: 集成SOAR等SQL优化工具
- **RESTful API**: 提供完整的REST API接口
- **Swagger文档**: 自动生成API文档
- **数据库迁移**: 自动数据库结构迁移
- **优雅启停**: 支持优雅启动和关闭

## 技术栈

- **语言**: Go 1.20+
- **框架**: Gin (HTTP路由)
- **ORM**: GORM (数据库操作)
- **数据库**: 支持MySQL、PostgreSQL、SQLite
- **缓存**: Redis (查询结果缓存)
- **任务调度**: Cron (定时任务)
- **文档**: Swagger/OpenAPI
- **日志**: Logrus
- **配置**: Viper (YAML配置)

## 快速开始

### 环境要求

- Go 1.20+
- 数据库 (MySQL/PostgreSQL/SQLite)
- Redis (可选)

### 本地开发

1. 克隆代码
```bash
git clone https://github.com/devops-microservices/sql-service.git
cd sql-service
```

2. 安装依赖
```bash
go mod download
```

3. 配置文件
```bash
cp config.yaml.example config.yaml
# 编辑config.yaml配置数据库连接等信息
```

4. 运行服务
```bash
go run main.go
```

5. 访问API文档
```
http://localhost:9983/swagger/index.html
```

### Docker部署

```bash
# 构建镜像
docker build -t sql-service .

# 运行容器
docker run -d \
  --name sql-service \
  -p 9983:9983 \
  -v $(pwd)/config.yaml:/app/config.yaml \
  sql-service
```

## API接口

### 数据库实例管理
- `GET /api/sql/instance` - 获取数据库实例列表
- `POST /api/sql/instance` - 创建数据库实例
- `GET /api/sql/instance/{id}` - 获取数据库实例详情
- `PUT /api/sql/instance/{id}` - 更新数据库实例
- `DELETE /api/sql/instance/{id}` - 删除数据库实例

### SQL查询
- `POST /api/sql/instance/{id}/query` - 执行SQL查询
- `POST /api/sql/instance/{id}/explain` - 获取SQL执行计划
- `GET /api/sql/instance/{id}/resource` - 获取数据库资源信息
- `GET /api/sql/download` - 下载查询结果

### SQL工作流
- `GET /api/sql/workflow` - 获取SQL工单列表
- `POST /api/sql/workflow` - 创建SQL工单
- `GET /api/sql/workflow/{id}` - 获取SQL工单详情
- `POST /api/sql/workflow/{id}/execute` - 执行SQL工单
- `POST /api/sql/workflow/rollback` - SQL回滚

### 查询日志
- `GET /api/sql/querylog` - 获取查询日志列表

### 数据脱敏
- `GET /api/sql/datamask` - 获取脱敏配置列表
- `POST /api/sql/datamask` - 创建脱敏配置

详细的API文档请访问: `http://localhost:9983/swagger/index.html`

## 数据模型

### 核心实体关系

```
DBInstance (数据库实例)
  ├── SqlWorkflow (SQL工单)
  ├── SqlQueryLog (查询日志)
  ├── DataMaskingColumns (脱敏配置)
  └── QueryPrivileges (查询权限)
```

### 主要数据表

- `sql_dbinstance` - 数据库实例表
- `sql_sqlworkflow` - SQL工单表
- `sql_sqlworkflowresult` - SQL工单结果表
- `sql_sqlquerylog` - SQL查询日志表
- `sql_sqlqueryadvisor` - SQL优化日志表
- `sql_datamaskingcolumns` - 数据脱敏配置表
- `sql_queryprivileges` - 查询权限表

## 开发指南

### 项目结构

```
sql-service/
├── config/          # 配置模块
├── controllers/     # 控制器层
├── models/          # 数据模型
├── services/        # 业务逻辑层
├── routes/          # 路由配置
├── engines/         # 数据库引擎抽象
├── docs/            # Swagger文档
├── main.go          # 入口文件
├── config.yaml      # 配置文件
├── Dockerfile       # Docker构建文件
├── go.mod           # Go模块定义
└── README.md        # 项目文档
```

### 添加新功能

1. 在 `models/` 中定义数据模型
2. 在 `services/` 中实现业务逻辑
3. 在 `controllers/` 中添加HTTP处理器
4. 在 `routes/` 中注册路由
5. 更新Swagger注释

### 构建和部署

```bash
# 本地构建
go build -o sql-service .

# 交叉编译
GOOS=linux GOARCH=amd64 go build -o sql-service-linux .

# Docker构建
docker build -t sql-service .
```

## 配置说明

主要配置项说明：

- `server`: HTTP服务器配置
- `database`: 数据库连接配置
- `redis`: Redis缓存配置
- `sql_query`: SQL查询相关配置
- `data_masking`: 数据脱敏配置
- `log`: 日志配置

## 许可证

Apache License 2.0
