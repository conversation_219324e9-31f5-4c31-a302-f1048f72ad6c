#!/bin/bash

# SQL Service 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 默认配置
SERVICE_NAME="sql-service"
CONFIG_FILE="config.yaml"
PORT=9983
SKIP_MIGRATION=false
FORCE_MIGRATE=false

# 帮助信息
show_help() {
    cat << EOF
SQL Service 启动脚本

用法: $0 [选项]

选项:
    -c, --config FILE       指定配置文件路径 (默认: config.yaml)
    -p, --port PORT         指定服务端口 (默认: 9983)
    -s, --skip-migration    跳过数据库迁移
    -f, --force-migrate     强制执行数据库迁移
    -h, --help              显示此帮助信息

示例:
    $0                      # 使用默认配置启动
    $0 -c custom.yaml       # 使用自定义配置文件
    $0 -p 8080              # 指定端口启动
    $0 -s                   # 跳过数据库迁移启动
    $0 -f                   # 强制执行数据库迁移

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -s|--skip-migration)
            SKIP_MIGRATION=true
            shift
            ;;
        -f|--force-migrate)
            FORCE_MIGRATE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查配置文件
check_config() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_warn "配置文件 $CONFIG_FILE 不存在"
        if [[ -f "config.yaml.example" ]]; then
            log_info "复制示例配置文件..."
            cp config.yaml.example "$CONFIG_FILE"
            log_warn "请编辑 $CONFIG_FILE 配置数据库连接等信息"
        else
            log_error "示例配置文件 config.yaml.example 也不存在"
            exit 1
        fi
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Go版本
    if ! command -v go &> /dev/null; then
        log_error "Go 未安装或不在PATH中"
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "Go版本: $GO_VERSION"
    
    # 检查服务二进制文件
    if [[ ! -f "./$SERVICE_NAME" ]]; then
        log_info "服务二进制文件不存在，开始构建..."
        go build -o "$SERVICE_NAME" .
        if [[ $? -ne 0 ]]; then
            log_error "构建失败"
            exit 1
        fi
        log_info "构建完成"
    fi
}

# 构建启动参数
build_args() {
    ARGS=""
    
    if [[ "$CONFIG_FILE" != "config.yaml" ]]; then
        ARGS="$ARGS --config $CONFIG_FILE"
    fi
    
    if [[ "$PORT" != "9983" ]]; then
        ARGS="$ARGS --port $PORT"
    fi
    
    if [[ "$SKIP_MIGRATION" == "true" ]]; then
        ARGS="$ARGS --skip-migration"
    fi
    
    if [[ "$FORCE_MIGRATE" == "true" ]]; then
        ARGS="$ARGS --force-migrate"
    fi
    
    echo "$ARGS"
}

# 启动服务
start_service() {
    local args=$(build_args)
    
    log_info "启动 $SERVICE_NAME..."
    log_info "配置文件: $CONFIG_FILE"
    log_info "服务端口: $PORT"
    log_info "启动参数: $args"
    
    # 启动服务
    exec ./"$SERVICE_NAME" $args
}

# 主函数
main() {
    log_info "=== SQL Service 启动脚本 ==="
    
    check_config
    check_dependencies
    start_service
}

# 信号处理
trap 'log_info "收到中断信号，正在关闭服务..."; exit 0' INT TERM

# 执行主函数
main "$@"
