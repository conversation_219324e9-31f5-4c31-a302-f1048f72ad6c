package services

import (
	"fmt"
	"time"

	"github.com/devops-microservices/sql-service/models"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// SqlService SQL服务接口
type SqlService interface {
	// DBInstance相关方法
	GetDBInstances(page, pageSize int, search string) ([]*models.DBInstance, int64, error)
	GetDBInstanceByID(id uint) (*models.DBInstance, error)
	GetDBInstanceByName(name string) (*models.DBInstance, error)
	CreateDBInstance(req *models.DBInstanceCreateRequest) (*models.DBInstance, error)
	UpdateDBInstance(id uint, updates map[string]interface{}) (*models.DBInstance, error)
	DeleteDBInstance(id uint) error
	GetUserInstances(username string, isAdmin bool) ([]*models.DBInstance, error)

	// SqlWorkflow相关方法
	GetSqlWorkflows(page, pageSize int, search string, status string) ([]*models.SqlWorkflow, int64, error)
	GetSqlWorkflowByID(id uint) (*models.SqlWorkflow, error)
	GetSqlWorkflowByOrderID(orderID string) (*models.SqlWorkflow, error)
	CreateSqlWorkflow(req *models.SqlWorkflowCreateRequest, username string) (*models.SqlWorkflow, error)
	UpdateSqlWorkflow(id uint, updates map[string]interface{}) (*models.SqlWorkflow, error)
	DeleteSqlWorkflow(id uint) error
	ExecuteSqlWorkflow(id uint, username string) error

	// SqlQueryLog相关方法
	GetSqlQueryLogs(page, pageSize int, search string, instanceID uint) ([]*models.SqlQueryLog, int64, error)
	GetSqlQueryLogByID(id uint) (*models.SqlQueryLog, error)
	CreateSqlQueryLog(log *models.SqlQueryLog) (*models.SqlQueryLog, error)

	// DataMaskingColumns相关方法
	GetDataMaskingColumns(page, pageSize int, search string, instanceID uint) ([]*models.DataMaskingColumns, int64, error)
	GetDataMaskingColumnsByID(id uint) (*models.DataMaskingColumns, error)
	CreateDataMaskingColumns(req *models.DataMaskingColumnsCreateRequest) (*models.DataMaskingColumns, error)
	UpdateDataMaskingColumns(id uint, updates map[string]interface{}) (*models.DataMaskingColumns, error)
	DeleteDataMaskingColumns(id uint) error

	// QueryPrivileges相关方法
	GetQueryPrivileges(page, pageSize int, search string, instanceID uint) ([]*models.QueryPrivileges, int64, error)
	GetQueryPrivilegesByID(id uint) (*models.QueryPrivileges, error)
	CreateQueryPrivileges(req *models.QueryPrivilegesCreateRequest) (*models.QueryPrivileges, error)
	UpdateQueryPrivileges(id uint, updates map[string]interface{}) (*models.QueryPrivileges, error)
	DeleteQueryPrivileges(id uint) error
	CheckUserQueryPrivilege(username string, instanceID uint, dbName string) bool
}

// SqlServiceImpl SQL服务实现
type SqlServiceImpl struct {
	db  *gorm.DB
	log *logrus.Logger
}

// NewSqlService 创建SQL服务实例
func NewSqlService(db *gorm.DB, log *logrus.Logger) SqlService {
	return &SqlServiceImpl{
		db:  db,
		log: log,
	}
}

// DBInstance相关方法实现

// GetDBInstances 获取数据库实例列表
func (s *SqlServiceImpl) GetDBInstances(page, pageSize int, search string) ([]*models.DBInstance, int64, error) {
	var instances []*models.DBInstance
	var total int64

	query := s.db.Model(&models.DBInstance{}).Where("is_deleted = ?", 0)

	if search != "" {
		query = query.Where("instance_name LIKE ? OR alias LIKE ? OR host LIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&instances).Error; err != nil {
		return nil, 0, err
	}

	return instances, total, nil
}

// GetDBInstanceByID 根据ID获取数据库实例
func (s *SqlServiceImpl) GetDBInstanceByID(id uint) (*models.DBInstance, error) {
	var instance models.DBInstance
	if err := s.db.Where("id = ? AND is_deleted = ?", id, 0).First(&instance).Error; err != nil {
		return nil, err
	}
	return &instance, nil
}

// GetDBInstanceByName 根据名称获取数据库实例
func (s *SqlServiceImpl) GetDBInstanceByName(name string) (*models.DBInstance, error) {
	var instance models.DBInstance
	if err := s.db.Where("instance_name = ? AND is_deleted = ?", name, 0).First(&instance).Error; err != nil {
		return nil, err
	}
	return &instance, nil
}

// CreateDBInstance 创建数据库实例
func (s *SqlServiceImpl) CreateDBInstance(req *models.DBInstanceCreateRequest) (*models.DBInstance, error) {
	instance := &models.DBInstance{
		InstanceName: req.InstanceName,
		Alias:        req.Alias,
		Type:         req.Type,
		DBType:       req.DBType,
		Mode:         req.Mode,
		Host:         req.Host,
		Port:         req.Port,
		User:         req.User,
		Password:     req.Password,
		DBName:       req.DBName,
		Charset:      req.Charset,
		AutoBackup:   req.AutoBackup,
		Projects:     req.Projects,
		Environments: req.Environments,
		IsDeleted:    0,
	}

	if err := s.db.Create(instance).Error; err != nil {
		return nil, err
	}

	s.log.Infof("创建数据库实例成功: %s", instance.InstanceName)
	return instance, nil
}

// UpdateDBInstance 更新数据库实例
func (s *SqlServiceImpl) UpdateDBInstance(id uint, updates map[string]interface{}) (*models.DBInstance, error) {
	var instance models.DBInstance
	if err := s.db.Where("id = ? AND is_deleted = ?", id, 0).First(&instance).Error; err != nil {
		return nil, err
	}

	if err := s.db.Model(&instance).Updates(updates).Error; err != nil {
		return nil, err
	}

	s.log.Infof("更新数据库实例成功: %s", instance.InstanceName)
	return &instance, nil
}

// DeleteDBInstance 删除数据库实例（软删除）
func (s *SqlServiceImpl) DeleteDBInstance(id uint) error {
	var instance models.DBInstance
	if err := s.db.Where("id = ? AND is_deleted = ?", id, 0).First(&instance).Error; err != nil {
		return err
	}

	if err := s.db.Model(&instance).Update("is_deleted", 1).Error; err != nil {
		return err
	}

	s.log.Infof("删除数据库实例成功: %s", instance.InstanceName)
	return nil
}

// GetUserInstances 获取用户有权限的数据库实例
func (s *SqlServiceImpl) GetUserInstances(username string, isAdmin bool) ([]*models.DBInstance, error) {
	var instances []*models.DBInstance

	if isAdmin {
		// 管理员可以看到所有实例
		if err := s.db.Where("is_deleted = ?", 0).Find(&instances).Error; err != nil {
			return nil, err
		}
	} else {
		// 普通用户只能看到有权限的实例
		var privileges []models.QueryPrivileges
		if err := s.db.Where("username = ? AND is_deleted = ? AND valid_date >= ?",
			username, 0, time.Now()).Find(&privileges).Error; err != nil {
			return nil, err
		}

		// 提取实例ID
		instanceIDs := make([]uint, 0)
		for _, priv := range privileges {
			instanceIDs = append(instanceIDs, priv.InstanceID)
		}

		if len(instanceIDs) > 0 {
			if err := s.db.Where("id IN ? AND is_deleted = ?", instanceIDs, 0).Find(&instances).Error; err != nil {
				return nil, err
			}
		}
	}

	return instances, nil
}

// generateOrderID 生成工单号
func (s *SqlServiceImpl) generateOrderID() string {
	return fmt.Sprintf("SQL_%d", time.Now().UnixNano())
}

// SqlWorkflow相关方法实现

// GetSqlWorkflows 获取SQL工单列表
func (s *SqlServiceImpl) GetSqlWorkflows(page, pageSize int, search string, status string) ([]*models.SqlWorkflow, int64, error) {
	var workflows []*models.SqlWorkflow
	var total int64

	query := s.db.Model(&models.SqlWorkflow{}).Preload("Instance")

	if search != "" {
		query = query.Where("title LIKE ? OR order_id LIKE ? OR engineer LIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if status != "" {
		query = query.Where("status = ?", status)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_time DESC").Find(&workflows).Error; err != nil {
		return nil, 0, err
	}

	return workflows, total, nil
}

// GetSqlWorkflowByID 根据ID获取SQL工单
func (s *SqlServiceImpl) GetSqlWorkflowByID(id uint) (*models.SqlWorkflow, error) {
	var workflow models.SqlWorkflow
	if err := s.db.Preload("Instance").Where("id = ?", id).First(&workflow).Error; err != nil {
		return nil, err
	}
	return &workflow, nil
}

// GetSqlWorkflowByOrderID 根据工单号获取SQL工单
func (s *SqlServiceImpl) GetSqlWorkflowByOrderID(orderID string) (*models.SqlWorkflow, error) {
	var workflow models.SqlWorkflow
	if err := s.db.Preload("Instance").Where("order_id = ?", orderID).First(&workflow).Error; err != nil {
		return nil, err
	}
	return &workflow, nil
}

// CreateSqlWorkflow 创建SQL工单
func (s *SqlServiceImpl) CreateSqlWorkflow(req *models.SqlWorkflowCreateRequest, username string) (*models.SqlWorkflow, error) {
	// 检查实例是否存在
	var instance models.DBInstance
	if err := s.db.Where("id = ? AND is_deleted = ?", req.InstanceID, 0).First(&instance).Error; err != nil {
		return nil, fmt.Errorf("数据库实例不存在")
	}

	workflow := &models.SqlWorkflow{
		OrderID:         s.generateOrderID(),
		Title:           req.Title,
		InstanceID:      req.InstanceID,
		DBName:          req.DBName,
		SchemaName:      req.SchemaName,
		IsBackup:        req.IsBackup,
		Engineer:        username,
		EngineerDisplay: username,                // TODO: 从用户服务获取显示名称
		Status:          "workflow_manreviewing", // 待审核状态
		SqlContent:      req.SqlContent,
		Method:          req.Method,
		ExpectTime:      req.ExpectTime,
		RelatedOID:      req.RelatedOID,
	}

	if err := s.db.Create(workflow).Error; err != nil {
		return nil, err
	}

	s.log.Infof("创建SQL工单成功: %s", workflow.OrderID)
	return workflow, nil
}

// UpdateSqlWorkflow 更新SQL工单
func (s *SqlServiceImpl) UpdateSqlWorkflow(id uint, updates map[string]interface{}) (*models.SqlWorkflow, error) {
	var workflow models.SqlWorkflow
	if err := s.db.Where("id = ?", id).First(&workflow).Error; err != nil {
		return nil, err
	}

	if err := s.db.Model(&workflow).Updates(updates).Error; err != nil {
		return nil, err
	}

	s.log.Infof("更新SQL工单成功: %s", workflow.OrderID)
	return &workflow, nil
}

// DeleteSqlWorkflow 删除SQL工单
func (s *SqlServiceImpl) DeleteSqlWorkflow(id uint) error {
	var workflow models.SqlWorkflow
	if err := s.db.Where("id = ?", id).First(&workflow).Error; err != nil {
		return err
	}

	if err := s.db.Delete(&workflow).Error; err != nil {
		return err
	}

	s.log.Infof("删除SQL工单成功: %s", workflow.OrderID)
	return nil
}

// ExecuteSqlWorkflow 执行SQL工单
func (s *SqlServiceImpl) ExecuteSqlWorkflow(id uint, username string) error {
	var workflow models.SqlWorkflow
	if err := s.db.Where("id = ?", id).First(&workflow).Error; err != nil {
		return err
	}

	// 检查工单状态
	if workflow.Status != "workflow_review_pass" {
		return fmt.Errorf("工单状态不允许执行")
	}

	// 更新工单状态为执行中
	if err := s.db.Model(&workflow).Update("status", "workflow_executing").Error; err != nil {
		return err
	}

	// TODO: 实际的SQL执行逻辑
	// 这里应该调用SQL引擎执行SQL语句

	s.log.Infof("开始执行SQL工单: %s", workflow.OrderID)
	return nil
}

// SqlQueryLog相关方法实现

// GetSqlQueryLogs 获取SQL查询日志列表
func (s *SqlServiceImpl) GetSqlQueryLogs(page, pageSize int, search string, instanceID uint) ([]*models.SqlQueryLog, int64, error) {
	var logs []*models.SqlQueryLog
	var total int64

	query := s.db.Model(&models.SqlQueryLog{}).Preload("Instance")

	if search != "" {
		query = query.Where("sqllog LIKE ? OR db_name LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if instanceID > 0 {
		query = query.Where("instance_id = ?", instanceID)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_time DESC").Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetSqlQueryLogByID 根据ID获取SQL查询日志
func (s *SqlServiceImpl) GetSqlQueryLogByID(id uint) (*models.SqlQueryLog, error) {
	var log models.SqlQueryLog
	if err := s.db.Preload("Instance").Where("id = ?", id).First(&log).Error; err != nil {
		return nil, err
	}
	return &log, nil
}

// CreateSqlQueryLog 创建SQL查询日志
func (s *SqlServiceImpl) CreateSqlQueryLog(log *models.SqlQueryLog) (*models.SqlQueryLog, error) {
	if err := s.db.Create(log).Error; err != nil {
		return nil, err
	}
	return log, nil
}

// DataMaskingColumns相关方法实现

// GetDataMaskingColumns 获取数据脱敏配置列表
func (s *SqlServiceImpl) GetDataMaskingColumns(page, pageSize int, search string, instanceID uint) ([]*models.DataMaskingColumns, int64, error) {
	var columns []*models.DataMaskingColumns
	var total int64

	query := s.db.Model(&models.DataMaskingColumns{}).Preload("Instance")

	if search != "" {
		query = query.Where("table_schema LIKE ? OR table_name LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if instanceID > 0 {
		query = query.Where("instance_id = ?", instanceID)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_time DESC").Find(&columns).Error; err != nil {
		return nil, 0, err
	}

	return columns, total, nil
}

// GetDataMaskingColumnsByID 根据ID获取数据脱敏配置
func (s *SqlServiceImpl) GetDataMaskingColumnsByID(id uint) (*models.DataMaskingColumns, error) {
	var columns models.DataMaskingColumns
	if err := s.db.Preload("Instance").Where("id = ?", id).First(&columns).Error; err != nil {
		return nil, err
	}
	return &columns, nil
}

// CreateDataMaskingColumns 创建数据脱敏配置
func (s *SqlServiceImpl) CreateDataMaskingColumns(req *models.DataMaskingColumnsCreateRequest) (*models.DataMaskingColumns, error) {
	// 检查实例是否存在
	var instance models.DBInstance
	if err := s.db.Where("id = ? AND is_deleted = ?", req.InstanceID, 0).First(&instance).Error; err != nil {
		return nil, fmt.Errorf("数据库实例不存在")
	}

	columns := &models.DataMaskingColumns{
		Active:      req.Active,
		InstanceID:  req.InstanceID,
		TableSchema: req.TableSchema,
		TableName:   req.TableName,
		Columns:     req.Columns,
	}

	if err := s.db.Create(columns).Error; err != nil {
		return nil, err
	}

	s.log.Infof("创建数据脱敏配置成功: %s.%s", columns.TableSchema, columns.TableName)
	return columns, nil
}

// UpdateDataMaskingColumns 更新数据脱敏配置
func (s *SqlServiceImpl) UpdateDataMaskingColumns(id uint, updates map[string]interface{}) (*models.DataMaskingColumns, error) {
	var columns models.DataMaskingColumns
	if err := s.db.Where("id = ?", id).First(&columns).Error; err != nil {
		return nil, err
	}

	if err := s.db.Model(&columns).Updates(updates).Error; err != nil {
		return nil, err
	}

	s.log.Infof("更新数据脱敏配置成功: %s.%s", columns.TableSchema, columns.TableName)
	return &columns, nil
}

// DeleteDataMaskingColumns 删除数据脱敏配置
func (s *SqlServiceImpl) DeleteDataMaskingColumns(id uint) error {
	var columns models.DataMaskingColumns
	if err := s.db.Where("id = ?", id).First(&columns).Error; err != nil {
		return err
	}

	if err := s.db.Delete(&columns).Error; err != nil {
		return err
	}

	s.log.Infof("删除数据脱敏配置成功: %s.%s", columns.TableSchema, columns.TableName)
	return nil
}

// QueryPrivileges相关方法实现

// GetQueryPrivileges 获取查询权限列表
func (s *SqlServiceImpl) GetQueryPrivileges(page, pageSize int, search string, instanceID uint) ([]*models.QueryPrivileges, int64, error) {
	var privileges []*models.QueryPrivileges
	var total int64

	query := s.db.Model(&models.QueryPrivileges{}).Preload("Instance").Where("is_deleted = ?", 0)

	if search != "" {
		query = query.Where("username LIKE ? OR user_display LIKE ? OR db_name LIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if instanceID > 0 {
		query = query.Where("instance_id = ?", instanceID)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_time DESC").Find(&privileges).Error; err != nil {
		return nil, 0, err
	}

	return privileges, total, nil
}

// GetQueryPrivilegesByID 根据ID获取查询权限
func (s *SqlServiceImpl) GetQueryPrivilegesByID(id uint) (*models.QueryPrivileges, error) {
	var privilege models.QueryPrivileges
	if err := s.db.Preload("Instance").Where("id = ? AND is_deleted = ?", id, 0).First(&privilege).Error; err != nil {
		return nil, err
	}
	return &privilege, nil
}

// CreateQueryPrivileges 创建查询权限
func (s *SqlServiceImpl) CreateQueryPrivileges(req *models.QueryPrivilegesCreateRequest) (*models.QueryPrivileges, error) {
	// 检查实例是否存在
	var instance models.DBInstance
	if err := s.db.Where("id = ? AND is_deleted = ?", req.InstanceID, 0).First(&instance).Error; err != nil {
		return nil, fmt.Errorf("数据库实例不存在")
	}

	privilege := &models.QueryPrivileges{
		Username:    req.Username,
		UserDisplay: req.UserDisplay,
		InstanceID:  req.InstanceID,
		DBName:      req.DBName,
		SchemaName:  req.SchemaName,
		ValidDate:   req.ValidDate,
		LimitNum:    req.LimitNum,
		IsDeleted:   0,
	}

	if err := s.db.Create(privilege).Error; err != nil {
		return nil, err
	}

	s.log.Infof("创建查询权限成功: %s@%s", privilege.Username, instance.InstanceName)
	return privilege, nil
}

// UpdateQueryPrivileges 更新查询权限
func (s *SqlServiceImpl) UpdateQueryPrivileges(id uint, updates map[string]interface{}) (*models.QueryPrivileges, error) {
	var privilege models.QueryPrivileges
	if err := s.db.Where("id = ? AND is_deleted = ?", id, 0).First(&privilege).Error; err != nil {
		return nil, err
	}

	if err := s.db.Model(&privilege).Updates(updates).Error; err != nil {
		return nil, err
	}

	s.log.Infof("更新查询权限成功: %s", privilege.Username)
	return &privilege, nil
}

// DeleteQueryPrivileges 删除查询权限（软删除）
func (s *SqlServiceImpl) DeleteQueryPrivileges(id uint) error {
	var privilege models.QueryPrivileges
	if err := s.db.Where("id = ? AND is_deleted = ?", id, 0).First(&privilege).Error; err != nil {
		return err
	}

	if err := s.db.Model(&privilege).Update("is_deleted", 1).Error; err != nil {
		return err
	}

	s.log.Infof("删除查询权限成功: %s", privilege.Username)
	return nil
}

// CheckUserQueryPrivilege 检查用户查询权限
func (s *SqlServiceImpl) CheckUserQueryPrivilege(username string, instanceID uint, dbName string) bool {
	var count int64
	s.db.Model(&models.QueryPrivileges{}).Where(
		"username = ? AND instance_id = ? AND db_name = ? AND is_deleted = ? AND valid_date >= ?",
		username, instanceID, dbName, 0, time.Now(),
	).Count(&count)

	return count > 0
}
