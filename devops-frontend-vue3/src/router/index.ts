import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 导入布局
import Layout from '@/layout/index.vue'

// 基础路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/login',
    component: () => import('@/views/auth/LoginView.vue'),
    name: 'Login',
    meta: { hidden: true }
  },
  {
    path: '/auth/register',
    component: () => import('@/views/auth/RegisterView.vue'),
    name: 'Register',
    meta: { hidden: true }
  },
  {
    path: '/auth/oauth2/callback',
    component: () => import('@/views/auth/OAuth2CallbackView.vue'),
    name: 'OAuth2Callback',
    meta: { hidden: true }
  },
  {
    path: '/404',
    component: () => import('@/views/error/404.vue'),
    name: '404',
    meta: { hidden: true }
  },

  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/IndexView.vue'),
        name: 'Dashboard',
        meta: { title: '仪表板', icon: 'House', affix: true }
      }
    ]
  }
]

// 动态路由
export const asyncRoutes: RouteRecordRaw[] = [
  // 微应用管理
  {
    path: '/microapp',
    component: Layout,
    name: 'MicroApp',
    meta: { title: '微应用管理', icon: 'Platform' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/microapp/IndexView.vue'),
        name: 'MicroAppList',
        meta: { title: '应用列表', icon: 'Memo' }
      },
      {
        path: 'instance',
        component: () => import('@/views/microapp/instance/IndexView.vue'),
        name: 'MicroAppInstance',
        meta: { title: '应用实例', icon: 'Connection' }
      },
      {
        path: 'language',
        component: () => import('@/views/cmdb/language/IndexView.vue'),
        name: 'MicroAppLanguage',
        meta: { title: '开发语言', icon: 'Document' }
      },
      {
        path: 'environment',
        component: () => import('@/views/cmdb/environment/IndexView.vue'),
        name: 'MicroAppEnvironment',
        meta: { title: '环境管理', icon: 'ScaleToOriginal' }
      },
      {
        path: 'project',
        component: () => import('@/views/cmdb/project/IndexView.vue'),
        name: 'MicroAppProject',
        meta: { title: '项目管理', icon: 'Folder' }
      },
      {
        path: 'product',
        component: () => import('@/views/cmdb/product/IndexView.vue'),
        name: 'MicroAppProduct',
        meta: { title: '产品管理', icon: 'SetUp' }
      },
      {
        path: 'region',
        component: () => import('@/views/cmdb/region/IndexView.vue'),
        name: 'MicroAppRegion',
        meta: { title: '区域管理', icon: 'LocationInformation' }
      },
      {
        path: 'team',
        component: () => import('@/views/microapp/team/IndexView.vue'),
        name: 'MicroAppTeam',
        meta: { title: '团队管理', icon: 'UserFilled' }
      }
    ]
  },

  // CI/CD管理
  {
    path: '/cicd',
    component: Layout,
    name: 'CICD',
    meta: { title: 'CI/CD管理', icon: 'Operation' },
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/cicd/dashboard/IndexView.vue'),
        name: 'CICDDashboard',
        meta: { title: '构建概览', icon: 'DataBoard' }
      },
      {
        path: 'pipeline',
        component: () => import('@/views/cicd/pipeline/IndexView.vue'),
        name: 'CICDPipeline',
        meta: { title: '流水线管理', icon: 'Connection' }
      },
      {
        path: 'pipeline/create',
        component: () => import('@/views/cicd/pipeline/CreateView.vue'),
        name: 'CICDPipelineCreate',
        meta: { title: '创建流水线', icon: 'Plus', activeMenu: '/cicd/pipeline' }
      },
      {
        path: 'pipeline/edit',
        component: () => import('@/views/cicd/pipeline/CreateView.vue'),
        name: 'CICDPipelineEdit',
        meta: { title: '编辑流水线', icon: 'Edit', activeMenu: '/cicd/pipeline' }
      },
      {
        path: 'pipeline/task',
        component: () => import('@/views/cicd/pipeline/TaskView.vue'),
        name: 'CICDPipelineTask',
        meta: { title: '流水线任务管理', icon: 'Connection' }
      },
      {
        path: 'build',
        component: () => import('@/views/cicd/build/IndexView.vue'),
        name: 'CICDBuild',
        meta: { title: '构建管理', icon: 'Tools' }
      }
    ]
  },

  // Kubernetes管理
  {
    path: '/kubernetes/cluster/:id/workload/terminal',
    component: () => import('@/views/kubernetes/workload/TerminalView.vue'),
    name: 'K8sClusterWorkloadTerminal',
    meta: { title: '工作负载终端', icon: 'Monitor', hidden: true }
  },
  {
    path: '/kubernetes/cluster/:id/workload/logs',
    component: () => import('@/views/kubernetes/workload/LogsView.vue'),
    name: 'K8sClusterWorkloadLogs',
    meta: { title: '工作负载日志', icon: 'Monitor', hidden: true }
  },
  {
    path: '/kubernetes',
    component: Layout,
    name: 'Kubernetes',
    meta: { title: 'Kubernetes', icon: 'Monitor' },
    children: [
      {
        path: 'cluster',
        component: () => import('@/views/kubernetes/cluster/IndexView.vue'),
        name: 'K8sCluster',
        meta: { title: '集群管理', icon: 'Monitor' }
      },
      {
        path: 'cluster/:id',
        component: () => import('@/views/kubernetes/cluster/DetailView.vue'),
        name: 'K8sClusterDetail',
        meta: { title: '集群详情', icon: 'Monitor' }
      },
      {
        path: 'cluster/:id/workload',
        component: () => import('@/views/kubernetes/workload/IndexView.vue'),
        name: 'K8sClusterWorkload',
        meta: { title: '工作负载', icon: 'Box', hidden: true }
      },
      {
        path: 'configmap',
        component: () => import('@/views/kubernetes/configmap/IndexView.vue'),
        name: 'K8sConfigMap',
        meta: { title: 'ConfigMap', icon: 'Setting' }
      }
    ]
  },

  // 工作流管理
  {
    path: '/workflow',
    component: Layout,
    name: 'Workflow',
    meta: { title: '工作流管理', icon: 'el-icon-s-order' },
    children: [
      {
        path: 'list',
        name: 'WorkflowList',
        component: () => import('@/views/workflow/list/IndexView.vue'),
        meta: { title: '工作流列表', icon: 'el-icon-document' }
      },
      {
        path: 'template',
        name: 'WorkflowTemplate',
        component: () => import('@/views/workflow/template/IndexView.vue'),
        meta: { title: '模板管理', icon: 'el-icon-s-grid' }
      },
      {
        path: 'category',
        name: 'WorkflowCategory',
        component: () => import('@/views/workflow/category/IndexView.vue'),
        meta: { title: '分类管理', icon: 'el-icon-folder' }
      },
      {
        path: 'callback',
        name: 'WorkflowCallback',
        component: () => import('@/views/workflow/callback/IndexView.vue'),
        meta: { title: '回调管理', icon: 'el-icon-link' }
      },
      {
        path: 'designer',
        name: 'WorkflowDesigner',
        component: () => import('@/views/workflow/designer/IndexView.vue'),
        meta: { title: '流程设计器', icon: 'el-icon-s-tools' }
      },
      {
        path: 'my-request',
        name: 'MyRequestWorkflows',
        component: () => import('@/views/workflow/my/RequestView.vue'),
        meta: { title: '我发起的', icon: 'el-icon-user' }
      },
      {
        path: 'my-upcoming',
        name: 'MyUpcomingWorkflows',
        component: () => import('@/views/workflow/my/UpcomingView.vue'),
        meta: { title: '我的待办', icon: 'el-icon-bell' }
      },
      {
        path: 'my-related',
        name: 'MyRelatedWorkflows',
        component: () => import('@/views/workflow/my/RelatedView.vue'),
        meta: { title: '我相关的', icon: 'el-icon-connection' }
      }
    ]
  },

  // 系统管理
  {
    path: '/system',
    component: Layout,
    name: 'System',
    meta: { title: '系统管理', icon: 'Setting' },
    children: [
      {
        path: 'user',
        component: () => import('@/views/system/user/IndexView.vue'),
        name: 'SystemUser',
        meta: { title: '用户管理', icon: 'User' }
      },
      // {
      //   path: 'role',
      //   component: () => import('@/views/system/role/IndexView.vue'),
      //   name: 'SystemRole',
      //   meta: { title: '角色管理', icon: 'UserFilled' }
      // },
      {
        path: 'rolegroup',
        component: () => import('@/views/system/rolegroup/IndexView.vue'),
        name: 'SystemRoleGroup',
        meta: { title: '角色组管理', icon: 'Avatar' }
      },
      {
        path: 'menu',
        component: () => import('@/views/system/menu/IndexView.vue'),
        name: 'SystemMenu',
        meta: { title: '菜单管理', icon: 'Menu' }
      },
      {
        path: 'permission',
        component: () => import('@/views/system/permission/IndexView.vue'),
        name: 'SystemPermission',
        meta: { title: '权限管理', icon: 'User' }
      },
      {
        path: 'organization',
        component: () => import('@/views/system/organization/IndexView.vue'),
        name: 'SystemOrganization',
        meta: { title: '组织管理', icon: 'User' }
      },
      // {
      //   path: 'config',
      //   component: () => import('@/views/system/config/IndexView.vue'),
      //   name: 'SystemConfig',
      //   meta: { title: '配置管理', icon: 'Setting' }
      // },
      {
        path: 'audit',
        component: () => import('@/views/system/audit/IndexView.vue'),
        name: 'SystemAudit',
        meta: { title: '审计日志', icon: 'User' }
      },
      // {
      //   path: 'log',
      //   component: () => import('@/views/system/log/IndexView.vue'),
      //   name: 'SystemLog',
      //   meta: { title: '日志管理', icon: 'User' }
      // },
      // {
      //   path: 'oauth',
      //   component: () => import('@/views/system/oauth/IndexView.vue'),
      //   name: 'SystemOAuth',
      //   meta: { title: 'OAuth配置', icon: 'Key' }
      // },
      // {
      //   path: 'ldap',
      //   component: () => import('@/views/system/ldap/IndexView.vue'),
      //   name: 'SystemLDAP',
      //   meta: { title: 'LDAP配置', icon: 'Connection' }
      // },
      {
        path: 'settings',
        component: () => import('@/views/system/settings/IndexView.vue'),
        name: 'SystemSettings',
        meta: { title: '系统设置', icon: 'Setting' }
      }
    ]
  },

  // SQL管理
  {
    path: '/sql/query',
    component: () => import('@/views/sql/SqlQueryView.vue'),
    name: 'SqlQuery',
    meta: { title: 'SQL查询', icon: 'Search' }
  },
  {
    path: '/sql',
    component: Layout,
    name: 'SQL',
    meta: { title: 'SQL管理', icon: 'Connection' },
    children: [
      {
        path: 'exec',
        component: () => import('@/views/sql/SqlQueryView.vue'),
        name: 'SqlExec',
        meta: { title: 'SQL执行', icon: 'Search' }
      }
    ]
  },

  // 监控管理
  {
    path: '/monitor',
    component: Layout,
    name: 'Monitor',
    meta: { title: '监控管理', icon: 'Monitor' },
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/monitor/dashboard/IndexView.vue'),
        name: 'MonitorDashboard',
        meta: { title: '监控概览', icon: 'PieChart' }
      },
      {
        path: 'logs',
        component: () => import('@/views/monitor/logs/IndexView.vue'),
        name: 'MonitorLogs',
        meta: { title: '日志查看', icon: 'Document' }
      }
    ]
  },

  // 404页面
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
    meta: { hidden: true }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: constantRoutes.concat(asyncRoutes),
  scrollBehavior: () => ({ left: 0, top: 0 })
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 不需要认证的页面
  const publicPages = ['/login', '/auth/register', '/auth/oauth2/callback', '/404']

  if (publicPages.includes(to.path)) {
    if (to.path === '/login' && userStore.isLoggedIn) {
      next({ path: '/' })
    } else {
      next()
    }
  } else {
    if (userStore.isLoggedIn) {
      // 如果用户已登录但用户信息为空，尝试获取用户信息
      if (!userStore.user) {
        try {
          await userStore.initUserInfo()
        } catch (error: any) {
          console.error('获取用户信息失败:', error)
          // 检查是否是认证错误（401），只有在认证失败时才重定向到登录页
          if (error?.response?.status === 401) {
            next(`/login?redirect=${to.path}`)
            return
          }
          // 其他错误（如网络问题）允许继续访问
          console.warn('用户信息获取失败，但允许继续访问:', error.message || error)
        }
      }
      next()
    } else {
      next(`/login?redirect=${to.path}`)
    }
  }
})

export default router
