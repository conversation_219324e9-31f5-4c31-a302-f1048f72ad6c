-- SQL Service 数据库初始化脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS opsdb;

-- 使用数据库
\c opsdb;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建示例数据库实例
INSERT INTO sql_dbinstance (
    instance_name, alias, type, db_type, mode, host, port, 
    user, password, db_name, charset, auto_backup, 
    projects, environments, is_deleted, created_time, update_time
) VALUES 
(
    'local-postgres', 
    '本地PostgreSQL实例', 
    'master', 
    'postgres', 
    'standalone', 
    'localhost', 
    5432, 
    'postgres', 
    'password', 
    'testdb', 
    'utf8', 
    0,
    '["default"]'::jsonb,
    '["development"]'::jsonb,
    0,
    NOW(),
    NOW()
),
(
    'local-mysql', 
    '本地MySQL实例', 
    'master', 
    'mysql', 
    'standalone', 
    'localhost', 
    3306, 
    'root', 
    'password', 
    'testdb', 
    'utf8mb4', 
    1,
    '["default"]'::jsonb,
    '["development"]'::jsonb,
    0,
    NOW(),
    NOW()
) ON CONFLICT (instance_name) DO NOTHING;

-- 创建示例查询权限
INSERT INTO sql_queryprivileges (
    username, user_display, instance_id, db_name, schema_name,
    valid_date, limit_num, is_deleted, created_time, update_time
) VALUES 
(
    'admin',
    '管理员',
    1,
    'testdb',
    'public',
    NOW() + INTERVAL '1 year',
    1000,
    0,
    NOW(),
    NOW()
),
(
    'developer',
    '开发者',
    1,
    'testdb',
    'public',
    NOW() + INTERVAL '6 months',
    100,
    0,
    NOW(),
    NOW()
) ON CONFLICT DO NOTHING;

-- 创建示例数据脱敏配置
INSERT INTO sql_datamaskingcolumns (
    active, instance_id, table_schema, table_name, columns,
    created_time, update_time
) VALUES 
(
    true,
    1,
    'public',
    'users',
    '[{"column_name": "phone", "rule_type": "phone"}, {"column_name": "email", "rule_type": "email"}]'::jsonb,
    NOW(),
    NOW()
) ON CONFLICT DO NOTHING;

-- 创建索引以提高性能
CREATE INDEX IF NOT EXISTS idx_sql_dbinstance_type ON sql_dbinstance(type);
CREATE INDEX IF NOT EXISTS idx_sql_dbinstance_db_type ON sql_dbinstance(db_type);
CREATE INDEX IF NOT EXISTS idx_sql_dbinstance_is_deleted ON sql_dbinstance(is_deleted);

CREATE INDEX IF NOT EXISTS idx_sql_sqlworkflow_status ON sql_sqlworkflow(status);
CREATE INDEX IF NOT EXISTS idx_sql_sqlworkflow_engineer ON sql_sqlworkflow(engineer);
CREATE INDEX IF NOT EXISTS idx_sql_sqlworkflow_instance_id ON sql_sqlworkflow(instance_id);

CREATE INDEX IF NOT EXISTS idx_sql_sqlquerylog_instance_id ON sql_sqlquerylog(instance_id);
CREATE INDEX IF NOT EXISTS idx_sql_sqlquerylog_created_time ON sql_sqlquerylog(created_time);

CREATE INDEX IF NOT EXISTS idx_sql_queryprivileges_username ON sql_queryprivileges(username);
CREATE INDEX IF NOT EXISTS idx_sql_queryprivileges_instance_id ON sql_queryprivileges(instance_id);
CREATE INDEX IF NOT EXISTS idx_sql_queryprivileges_valid_date ON sql_queryprivileges(valid_date);
CREATE INDEX IF NOT EXISTS idx_sql_queryprivileges_is_deleted ON sql_queryprivileges(is_deleted);

CREATE INDEX IF NOT EXISTS idx_sql_datamaskingcolumns_instance_id ON sql_datamaskingcolumns(instance_id);
CREATE INDEX IF NOT EXISTS idx_sql_datamaskingcolumns_active ON sql_datamaskingcolumns(active);

-- 创建视图以便于查询
CREATE OR REPLACE VIEW v_active_instances AS
SELECT 
    id,
    instance_name,
    alias,
    type,
    db_type,
    mode,
    host,
    port,
    db_name,
    created_time,
    update_time
FROM sql_dbinstance 
WHERE is_deleted = 0;

CREATE OR REPLACE VIEW v_user_privileges AS
SELECT 
    p.username,
    p.user_display,
    i.instance_name,
    i.alias as instance_alias,
    p.db_name,
    p.schema_name,
    p.valid_date,
    p.limit_num,
    p.created_time
FROM sql_queryprivileges p
JOIN sql_dbinstance i ON p.instance_id = i.id
WHERE p.is_deleted = 0 
  AND i.is_deleted = 0 
  AND p.valid_date >= NOW();

-- 创建函数用于清理过期数据
CREATE OR REPLACE FUNCTION cleanup_expired_data()
RETURNS void AS $$
BEGIN
    -- 清理过期的查询权限
    UPDATE sql_queryprivileges 
    SET is_deleted = 1 
    WHERE valid_date < NOW() - INTERVAL '30 days' 
      AND is_deleted = 0;
    
    -- 清理旧的查询日志（保留3个月）
    DELETE FROM sql_sqlquerylog 
    WHERE created_time < NOW() - INTERVAL '3 months';
    
    -- 清理旧的SQL优化日志（保留1个月）
    DELETE FROM sql_sqlqueryadvisor 
    WHERE created_time < NOW() - INTERVAL '1 month';
    
    RAISE NOTICE '过期数据清理完成';
END;
$$ LANGUAGE plpgsql;

-- 创建定时清理任务（需要pg_cron扩展）
-- SELECT cron.schedule('cleanup-expired-data', '0 2 * * *', 'SELECT cleanup_expired_data();');

COMMIT;
