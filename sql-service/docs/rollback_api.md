# SQL回滚功能API文档

## 概述

SQL回滚功能提供了对已执行SQL工单的回滚操作，支持自动生成回滚SQL和手动执行回滚。

## API接口

### 1. 获取回滚记录列表

```http
GET /api/v1/rollback?page=1&page_size=10&workflow_id=123
```

**参数：**
- `page`: 页码（可选，默认1）
- `page_size`: 每页数量（可选，默认10）
- `workflow_id`: 工单ID（可选，用于筛选）

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [
      {
        "id": 1,
        "workflow_id": 123,
        "instance_id": 1,
        "db_name": "test_db",
        "backup_db_name": "backup_test_db_123",
        "sql_content": "INSERT INTO users (name, email) VALUES ('test', '<EMAIL>')",
        "rollback_sql": "-- INSERT语句的回滚SQL（需要手动确认）:\n-- 请根据实际插入的数据生成对应的DELETE语句\n-- 原始SQL: INSERT INTO users (name, email) VALUES ('test', '<EMAIL>')",
        "status": "pending",
        "executed_by": "admin",
        "executed_at": null,
        "error_msg": "",
        "created_at": "2025-01-14T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10,
    "total_pages": 1
  }
}
```

### 2. 获取回滚记录详情

```http
GET /api/v1/rollback/1
```

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "workflow_id": 123,
    "instance_id": 1,
    "db_name": "test_db",
    "backup_db_name": "backup_test_db_123",
    "sql_content": "INSERT INTO users (name, email) VALUES ('test', '<EMAIL>')",
    "rollback_sql": "DELETE FROM users WHERE name='test' AND email='<EMAIL>'",
    "status": "pending",
    "executed_by": "admin",
    "executed_at": null,
    "error_msg": "",
    "created_at": "2025-01-14T10:00:00Z",
    "workflow": {
      "id": 123,
      "order_id": "ORDER_20250114_001",
      "title": "添加测试用户"
    },
    "instance": {
      "id": 1,
      "instance_name": "测试数据库",
      "host": "localhost",
      "port": 3306
    }
  }
}
```

### 3. 创建回滚记录

```http
POST /api/v1/rollback
Content-Type: application/json

{
  "workflow_id": 123,
  "reason": "数据错误，需要回滚"
}
```

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "workflow_id": 123,
    "status": "pending",
    "rollback_sql": "-- INSERT语句的回滚SQL（需要手动确认）:\n-- 请根据实际插入的数据生成对应的DELETE语句",
    "created_at": "2025-01-14T10:00:00Z"
  }
}
```

### 4. 执行回滚

```http
POST /api/v1/rollback/1/execute
```

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "回滚执行成功"
  }
}
```

### 5. 获取工单的回滚记录

```http
GET /api/v1/workflow/123/rollbacks
```

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "workflow_id": 123,
      "status": "success",
      "executed_by": "admin",
      "executed_at": "2025-01-14T10:05:00Z",
      "created_at": "2025-01-14T10:00:00Z"
    }
  ]
}
```

### 6. 检查工单是否可以回滚

```http
GET /api/v1/workflow/123/can-rollback
```

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "can_rollback": true,
    "reason": "可以回滚"
  }
}
```

## 回滚状态说明

- `pending`: 待执行
- `executing`: 执行中
- `success`: 执行成功
- `failed`: 执行失败

## 权限要求

- 创建回滚记录：需要登录用户权限
- 执行回滚：需要管理员权限
- 查看回滚记录：需要登录用户权限

## 注意事项

1. 只有状态为`workflow_finish`的工单才能创建回滚记录
2. 每个工单只能创建一个回滚记录
3. 当前版本的回滚SQL生成是基础实现，建议在执行前手动验证
4. 回滚操作不可逆，请谨慎操作
5. 建议在执行回滚前先在测试环境验证

## 错误码说明

- `400`: 请求参数错误
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 使用示例

### 完整的回滚流程

1. **检查工单是否可以回滚**
```bash
curl -X GET "http://localhost:8080/api/v1/workflow/123/can-rollback" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

2. **创建回滚记录**
```bash
curl -X POST "http://localhost:8080/api/v1/rollback" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"workflow_id": 123, "reason": "数据错误"}'
```

3. **查看生成的回滚SQL**
```bash
curl -X GET "http://localhost:8080/api/v1/rollback/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

4. **执行回滚（管理员权限）**
```bash
curl -X POST "http://localhost:8080/api/v1/rollback/1/execute" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

5. **查看回滚结果**
```bash
curl -X GET "http://localhost:8080/api/v1/rollback/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
