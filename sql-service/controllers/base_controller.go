package controllers

import (
	"net/http"
	"strconv"

	"github.com/devops-microservices/sql-service/models"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// BaseController 基础控制器
type BaseController struct {
	log *logrus.Logger
}

// NewBaseController 创建基础控制器
func NewBaseController(log *logrus.Logger) *BaseController {
	return &BaseController{
		log: log,
	}
}

// SuccessResponse 成功响应
func (bc *BaseController) SuccessResponse(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, models.APIResponse{
		Code:    200,
		Message: "success",
		Data:    data,
	})
}

// ErrorResponse 错误响应
func (bc *BaseController) ErrorResponse(c *gin.Context, code int, message string) {
	c.JSON(http.StatusOK, models.APIResponse{
		Code:    code,
		Message: message,
	})
}

// BadRequestResponse 400错误响应
func (bc *BaseController) BadRequestResponse(c *gin.Context, message string) {
	bc.ErrorResponse(c, 400, message)
}

// NotFoundResponse 404错误响应
func (bc *BaseController) NotFoundResponse(c *gin.Context, message string) {
	bc.ErrorResponse(c, 404, message)
}

// InternalErrorResponse 500错误响应
func (bc *BaseController) InternalErrorResponse(c *gin.Context, message string) {
	bc.ErrorResponse(c, 500, message)
}

// GetPaginationParams 获取分页参数
func (bc *BaseController) GetPaginationParams(c *gin.Context) (page, pageSize int) {
	page = 1
	pageSize = 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if sizeStr := c.Query("page_size"); sizeStr != "" {
		if s, err := strconv.Atoi(sizeStr); err == nil && s > 0 && s <= 100 {
			pageSize = s
		}
	}

	return page, pageSize
}

// GetSearchParam 获取搜索参数
func (bc *BaseController) GetSearchParam(c *gin.Context) string {
	return c.Query("search")
}

// GetIDParam 获取ID参数
func (bc *BaseController) GetIDParam(c *gin.Context) (uint, error) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return 0, err
	}
	return uint(id), nil
}

// PaginatedResponse 分页响应
func (bc *BaseController) PaginatedResponse(c *gin.Context, items interface{}, total int64, page, pageSize int) {
	totalPages := int(total) / pageSize
	if int(total)%pageSize > 0 {
		totalPages++
	}

	response := models.PaginatedResponse{
		Items:      items,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}

	bc.SuccessResponse(c, response)
}

// ValidateJSON 验证JSON请求体
func (bc *BaseController) ValidateJSON(c *gin.Context, obj interface{}) bool {
	if err := c.ShouldBindJSON(obj); err != nil {
		bc.BadRequestResponse(c, "请求参数格式错误: "+err.Error())
		return false
	}
	return true
}

// GetCurrentUser 获取当前用户（从JWT或其他认证方式）
func (bc *BaseController) GetCurrentUser(c *gin.Context) string {
	// TODO: 从JWT token或其他认证方式获取用户信息
	// 这里暂时从header中获取
	username := c.GetHeader("X-Username")
	if username == "" {
		username = "admin" // 默认用户
	}
	return username
}

// IsAdmin 检查是否为管理员
func (bc *BaseController) IsAdmin(c *gin.Context) bool {
	// TODO: 实现管理员权限检查逻辑
	// 这里暂时从header中获取
	isAdmin := c.GetHeader("X-Is-Admin")
	return isAdmin == "true"
}

// LogRequest 记录请求日志
func (bc *BaseController) LogRequest(c *gin.Context, action string) {
	bc.log.WithFields(logrus.Fields{
		"method":     c.Request.Method,
		"path":       c.Request.URL.Path,
		"ip":         c.ClientIP(),
		"user_agent": c.GetHeader("User-Agent"),
		"action":     action,
		"user":       bc.GetCurrentUser(c),
	}).Info("API Request")
}
