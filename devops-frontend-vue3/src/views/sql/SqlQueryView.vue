<template>
    <div class="sql-query-page">
        <!-- 顶部工具栏 -->
        <div class="query-toolbar">
            <div class="toolbar-left">
                <el-select v-model="selectedInstance" placeholder="选择数据库实例" style="width: 200px"
                    @change="onInstanceChange">
                    <el-option v-for="instance in instances" :key="instance.id"
                        :label="instance.alias || instance.instance_name" :value="instance.id" />
                </el-select>

                <el-select v-model="selectedDatabase" placeholder="选择数据库" style="width: 150px; margin-left: 10px"
                    :disabled="!selectedInstance" @change="onDatabaseChange">
                    <el-option v-for="db in databases" :key="db.database_name" :label="db.database_name"
                        :value="db.database_name" />
                </el-select>

                <el-button type="primary" :icon="VideoPlay" :loading="queryLoading"
                    :disabled="!selectedInstance || !selectedDatabase || !sqlContent.trim()" @click="executeQuery"
                    style="margin-left: 10px">
                    执行查询
                </el-button>

                <el-button :icon="Document" :disabled="!selectedInstance || !selectedDatabase || !sqlContent.trim()"
                    @click="explainQuery">
                    执行计划
                </el-button>

                <el-button :icon="Refresh" @click="clearResult">
                    清空结果
                </el-button>

                <el-button :icon="Download" :disabled="!queryResult || !queryResult.rows.length"
                    @click="downloadResult">
                    导出结果
                </el-button>
            </div>

            <div class="toolbar-right">
                <span class="status-info" v-if="queryResult">
                    影响行数: {{ queryResult.affected_rows }} | 查询时间: {{ queryResult.query_time }}ms
                </span>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="query-content">
            <!-- 左侧数据库结构树 -->
            <div class="database-sidebar">
                <div class="sidebar-header">
                    <span class="header-title">数据库实例</span>
                    <el-button :icon="Refresh" size="small" text @click="refreshDatabaseTree" title="刷新" />
                </div>

                <div class="tree-container">
                    <el-tree :data="databaseTree" :props="treeProps" node-key="id" :expand-on-click-node="false"
                        @node-click="onTreeNodeClick" class="database-tree">
                        <template #default="{ node, data }">
                            <span class="tree-node">
                                <el-icon class="node-icon">
                                    <Connection v-if="data.type === 'instance'" />
                                    <Folder v-else-if="data.type === 'database'" />
                                    <Document v-else-if="data.type === 'table'" />
                                </el-icon>
                                <span class="node-label">{{ node.label }}</span>
                            </span>
                        </template>
                    </el-tree>
                </div>
            </div>

            <!-- 右侧查询区域 -->
            <div class="query-main">
                <!-- SQL编辑器区域 -->
                <div class="editor-section">
                    <div class="section-header">
                        <span class="section-title">SQL查询</span>
                        <div class="header-actions">
                            <el-button size="small" text @click="formatSql">格式化</el-button>
                            <el-button size="small" text @click="clearSql">清空</el-button>
                        </div>
                    </div>

                    <div class="editor-wrapper">
                        <MonacoEditor v-model="sqlContent" :height="editorHeight" language="sql" theme="vs-dark"
                            :options="editorOptions" @change="onSqlChange" />
                    </div>
                </div>

                <!-- 查询结果区域 -->
                <div class="result-section">
                    <div class="section-header">
                        <span class="section-title">查询结果</span>
                        <div class="header-actions" v-if="queryResult && queryResult.rows.length > 0">
                            <el-button size="small" text @click="exportToCsv">导出CSV</el-button>
                            <el-button size="small" text @click="exportToExcel">导出Excel</el-button>
                        </div>
                    </div>

                    <div class="result-wrapper">
                        <el-alert v-if="queryError" :title="queryError" type="error" show-icon :closable="false"
                            class="error-alert" />

                        <el-table v-if="queryResult && queryResult.rows.length > 0" :data="tableData" border stripe
                            :height="tableHeight" style="width: 100%" class="result-table">
                            <el-table-column v-for="(column, index) in queryResult.column_list" :key="index"
                                :prop="`col_${index}`" :label="column" min-width="120" show-overflow-tooltip />
                        </el-table>

                        <el-empty v-if="!queryResult || (queryResult.rows.length === 0 && !queryError)"
                            description="暂无查询结果" class="empty-result" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
    VideoPlay,
    Document,
    Refresh,
    Download,
    Folder,
    Connection
} from '@element-plus/icons-vue'
import { sqlApi, type DBInstance, type DatabaseInfo, type QueryResult, type SqlQueryRequest } from '../../api/modules/sql'
import { handleAPIResponse } from '../../utils/response'
import MonacoEditor from '../../components/MonacoEditor.vue'

// 响应式数据
const selectedInstance = ref<number | null>(null)
const selectedDatabase = ref<string>('')
const instances = ref<DBInstance[]>([])
const databases = ref<DatabaseInfo[]>([])
const sqlContent = ref<string>('')
const queryResult = ref<QueryResult | null>(null)
const queryError = ref<string>('')
const queryLoading = ref<boolean>(false)

// 编辑器相关
const editorHeight = ref<string>('300px')
const tableHeight = ref<number>(400)

// 编辑器选项
const editorOptions = ref({
    fontSize: 14,
    wordWrap: 'on' as const,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    automaticLayout: true,
    theme: 'vs-dark'
})

// 数据库树相关
const databaseTree = ref<any[]>([])
const treeProps = {
    children: 'children',
    label: 'label'
}

// 计算属性 - 表格数据
const tableData = computed(() => {
    if (!queryResult.value || !queryResult.value.rows.length) {
        return []
    }

    return queryResult.value.rows.map((row: any[]) => {
        const obj: any = {}
        row.forEach((cell: any, index: number) => {
            obj[`col_${index}`] = cell
        })
        return obj
    })
})

// 生命周期
onMounted(() => {
    loadInstances()
})

// 方法
const loadInstances = async () => {
    try {
        const response = await sqlApi.getDBInstances()
        const result = handleAPIResponse(response) as any
        console.log(result, '@@@@@@@')
        if (result && result.items) {
            instances.value = result.items || []
        }
    } catch (error) {
        console.error('加载数据库实例失败:', error)
        ElMessage.error('加载数据库实例失败')
    }
}

const onInstanceChange = async (instanceId: number) => {
    selectedDatabase.value = ''
    databases.value = []
    queryResult.value = null
    queryError.value = ''

    if (instanceId) {
        await loadDatabases(instanceId)
        await loadDatabaseTree(instanceId)
    }
}

const loadDatabases = async (instanceId: number) => {
    try {
        const response = await sqlApi.getDatabases(instanceId)
        if (response.data && response.data.data) {
            databases.value = response.data.data || []
        }
    } catch (error) {
        console.error('加载数据库列表失败:', error)
        ElMessage.error('加载数据库列表失败')
    }
}

const onDatabaseChange = () => {
    queryResult.value = null
    queryError.value = ''
}

const executeQuery = async () => {
    if (!selectedInstance.value || !selectedDatabase.value || !sqlContent.value.trim()) {
        ElMessage.warning('请选择数据库实例和数据库，并输入SQL语句')
        return
    }

    queryLoading.value = true
    queryError.value = ''

    try {
        const queryRequest: SqlQueryRequest = {
            sql_content: sqlContent.value.trim(),
            db_name: selectedDatabase.value,
            limit_num: 1000
        }

        const response = await sqlApi.executeQuery(selectedInstance.value, queryRequest)
        if (response.data && response.data.data) {
            queryResult.value = response.data.data
            if (response.data.data.error) {
                queryError.value = response.data.data.error
            } else {
                ElMessage.success(`查询成功，返回 ${response.data.data.affected_rows} 行数据`)
            }
        }
    } catch (error: any) {
        console.error('执行查询失败:', error)
        queryError.value = error.message || '执行查询失败'
        ElMessage.error('执行查询失败')
    } finally {
        queryLoading.value = false
    }
}

const explainQuery = async () => {
    if (!selectedInstance.value || !selectedDatabase.value || !sqlContent.value.trim()) {
        ElMessage.warning('请选择数据库实例和数据库，并输入SQL语句')
        return
    }

    try {
        const queryRequest: SqlQueryRequest = {
            sql_content: sqlContent.value.trim(),
            db_name: selectedDatabase.value
        }

        const response = await sqlApi.explainQuery(selectedInstance.value, queryRequest)
        if (response.data && response.data.data) {
            queryResult.value = response.data.data
            ElMessage.success('获取执行计划成功')
        }
    } catch (error: any) {
        console.error('获取执行计划失败:', error)
        queryError.value = error.message || '获取执行计划失败'
        ElMessage.error('获取执行计划失败')
    }
}

const clearResult = () => {
    queryResult.value = null
    queryError.value = ''
}

const clearSql = () => {
    sqlContent.value = ''
}

const formatSql = () => {
    // 简单的SQL格式化
    if (sqlContent.value.trim()) {
        const formatted = sqlContent.value
            .replace(/\s+/g, ' ')
            .replace(/\s*,\s*/g, ',\n  ')
            .replace(/\s*(SELECT|FROM|WHERE|GROUP BY|ORDER BY|HAVING|LIMIT)\s+/gi, '\n$1 ')
            .replace(/\s*(AND|OR)\s+/gi, '\n  $1 ')
            .trim()

        sqlContent.value = formatted
    }
}

// SQL编辑器变化事件
const onSqlChange = (value: string) => {
    sqlContent.value = value
}

// 导出功能
const exportToCsv = () => {
    if (!queryResult.value || !queryResult.value.rows.length) {
        ElMessage.warning('没有可导出的数据')
        return
    }
    ElMessage.info('CSV导出功能开发中...')
}

const exportToExcel = () => {
    if (!queryResult.value || !queryResult.value.rows.length) {
        ElMessage.warning('没有可导出的数据')
        return
    }
    ElMessage.info('Excel导出功能开发中...')
}

const downloadResult = () => {
    if (!queryResult.value || !queryResult.value.rows.length) {
        ElMessage.warning('没有可导出的数据')
        return
    }

    // 这里可以实现CSV导出功能
    ElMessage.info('导出功能开发中...')
}

// 加载数据库树结构
const loadDatabaseTree = async (instanceId: number) => {
    try {
        const instance = instances.value.find(inst => inst.id === instanceId)
        if (!instance) return

        const response = await sqlApi.getDatabases(instanceId)
        if (response.data && response.data.data) {
            const databases = response.data.data

            const treeData = [{
                id: `instance_${instanceId}`,
                label: instance.alias || instance.instance_name,
                type: 'instance',
                children: databases.map((db: any) => ({
                    id: `db_${instanceId}_${db.database_name}`,
                    label: db.database_name,
                    type: 'database',
                    instanceId,
                    dbName: db.database_name,
                    children: []
                }))
            }]

            databaseTree.value = treeData
        }
    } catch (error) {
        console.error('加载数据库树失败:', error)
    }
}

// 刷新数据库树
const refreshDatabaseTree = () => {
    if (selectedInstance.value) {
        loadDatabaseTree(selectedInstance.value)
    }
}

// 树节点点击事件
const onTreeNodeClick = async (data: any) => {
    if (data.type === 'database') {
        selectedInstance.value = data.instanceId
        selectedDatabase.value = data.dbName

        // 加载表列表
        try {
            const response = await sqlApi.getTables(data.instanceId, { db_name: data.dbName })
            if (response.data && response.data.data) {
                const tables = response.data.data

                // 更新树结构，添加表节点
                const updateTreeNode = (nodes: any[]): any[] => {
                    return nodes.map(node => {
                        if (node.id === data.id) {
                            return {
                                ...node,
                                children: tables.map((table: string) => ({
                                    id: `table_${data.instanceId}_${data.dbName}_${table}`,
                                    label: table,
                                    type: 'table',
                                    instanceId: data.instanceId,
                                    dbName: data.dbName,
                                    tableName: table
                                }))
                            }
                        } else if (node.children) {
                            return {
                                ...node,
                                children: updateTreeNode(node.children)
                            }
                        }
                        return node
                    })
                }

                databaseTree.value = updateTreeNode(databaseTree.value)
            }
        } catch (error) {
            console.error('加载表列表失败:', error)
        }
    } else if (data.type === 'table') {
        // 点击表时，生成SELECT语句
        sqlContent.value = `SELECT * FROM ${data.tableName} LIMIT 100;`
    }
}
</script>

<style scoped>
.sql-query-page {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f7fa;
}

/* 顶部工具栏 */
.query-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: white;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toolbar-right {
    display: flex;
    align-items: center;
}

.status-info {
    font-size: 12px;
    color: #909399;
    margin-left: 16px;
}

/* 主要内容区域 */
.query-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 左侧数据库树 */
.database-sidebar {
    width: 280px;
    background-color: white;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
    background-color: #fafbfc;
}

.header-title {
    font-weight: 600;
    color: #303133;
    font-size: 14px;
}

.tree-container {
    flex: 1;
    padding: 8px;
    overflow-y: auto;
}

.database-tree {
    height: 100%;
}

.tree-node {
    display: flex;
    align-items: center;
    gap: 6px;
}

.node-icon {
    font-size: 16px;
    color: #606266;
}

.node-label {
    font-size: 13px;
    color: #606266;
}

/* 右侧查询区域 */
.query-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* SQL编辑器区域 */
.editor-section {
    height: 350px;
    background-color: white;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 16px;
    background-color: #fafbfc;
    border-bottom: 1px solid #e4e7ed;
}

.section-title {
    font-weight: 600;
    color: #303133;
    font-size: 14px;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.editor-wrapper {
    flex: 1;
    padding: 0;
    overflow: hidden;
}

/* 查询结果区域 */
.result-section {
    flex: 1;
    background-color: white;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.result-wrapper {
    flex: 1;
    padding: 16px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.error-alert {
    margin-bottom: 16px;
}

.result-table {
    flex: 1;
}

.empty-result {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .database-sidebar {
        width: 240px;
    }

    .editor-section {
        height: 300px;
    }
}

@media (max-width: 768px) {
    .query-content {
        flex-direction: column;
    }

    .database-sidebar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid #e4e7ed;
    }

    .query-toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .toolbar-left {
        justify-content: center;
        flex-wrap: wrap;
    }

    .toolbar-right {
        justify-content: center;
    }

    .editor-section {
        height: 250px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .sql-query-page {
        background-color: #1a1a1a;
    }

    .query-toolbar,
    .database-sidebar,
    .editor-section,
    .result-section {
        background-color: #2d2d2d;
        border-color: #404040;
    }

    .sidebar-header,
    .section-header {
        background-color: #3a3a3a;
        border-color: #404040;
    }

    .header-title,
    .section-title {
        color: #e4e7ed;
    }

    .node-icon,
    .node-label {
        color: #c0c4cc;
    }

    .status-info {
        color: #909399;
    }
}

/* 滚动条样式 */
.tree-container::-webkit-scrollbar,
.result-wrapper::-webkit-scrollbar {
    width: 6px;
}

.tree-container::-webkit-scrollbar-track,
.result-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.tree-container::-webkit-scrollbar-thumb,
.result-wrapper::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.tree-container::-webkit-scrollbar-thumb:hover,
.result-wrapper::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>