package controllers

import (
	"strconv"
	"strings"

	"github.com/devops-microservices/sql-service/config"
	"github.com/devops-microservices/sql-service/engines"
	"github.com/devops-microservices/sql-service/models"
	"github.com/devops-microservices/sql-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// QueryController SQL查询控制器
type QueryController struct {
	*BaseController
	service services.SqlService
	config  *config.Config
}

// NewQueryController 创建SQL查询控制器
func NewQueryController(service services.SqlService, cfg *config.Config, log *logrus.Logger) *QueryController {
	return &QueryController{
		BaseController: NewBaseController(log),
		service:        service,
		config:         cfg,
	}
}

// ExecuteQuery 执行SQL查询
// @Summary 执行SQL查询
// @Description 在指定数据库实例上执行SQL查询
// @Tags SQL查询
// @Accept json
// @Produce json
// @Param id path int true "实例ID"
// @Param query body models.SqlQueryRequest true "查询请求"
// @Success 200 {object} models.APIResponse{data=models.QueryResult}
// @Router /instance/{id}/query [post]
func (qc *QueryController) ExecuteQuery(c *gin.Context) {
	qc.LogRequest(c, "执行SQL查询")

	id, err := qc.GetIDParam(c)
	if err != nil {
		qc.BadRequestResponse(c, "无效的实例ID")
		return
	}

	var req models.SqlQueryRequest
	if !qc.ValidateJSON(c, &req) {
		return
	}

	// 检查实例是否存在
	instance, err := qc.service.GetDBInstanceByID(id)
	if err != nil {
		qc.NotFoundResponse(c, "数据库实例不存在")
		return
	}

	// 检查用户权限
	username := qc.GetCurrentUser(c)
	isAdmin := qc.IsAdmin(c)
	if !isAdmin && !qc.service.CheckUserQueryPrivilege(username, id, req.DBName) {
		qc.ErrorResponse(c, 403, "没有查询权限")
		return
	}

	// 使用SQL引擎执行查询（支持GoInception）
	var engine engines.DatabaseEngine
	if qc.config.GoInception.Enabled && instance.DBType == "mysql" {
		engine, err = engines.GetEngineWithConfig(instance, &qc.config.GoInception)
	} else {
		engine, err = engines.GetEngine(instance)
	}
	if err != nil {
		qc.log.Errorf("获取数据库引擎失败: %v", err)
		qc.InternalErrorResponse(c, "获取数据库引擎失败")
		return
	}
	defer engine.Close()

	// 执行SQL查询
	result, err := engine.Query(req.DBName, req.SqlContent, req.LimitNum, req.SchemaName, req.TbName, 60)
	if err != nil {
		qc.log.Errorf("执行SQL查询失败: %v", err)
		qc.InternalErrorResponse(c, "执行SQL查询失败: "+err.Error())
		return
	}

	// 如果查询有错误，返回错误信息
	if result.Error != "" {
		qc.ErrorResponse(c, 400, "SQL执行错误: "+result.Error)
		return
	}

	// 应用数据脱敏
	maskedResult := result
	hitRule := false
	masking := false

	// 检查是否需要脱敏（仅对SELECT查询）
	sqlUpper := strings.ToUpper(strings.TrimSpace(req.SqlContent))
	if len(sqlUpper) >= 6 && sqlUpper[:6] == "SELECT" {
		maskingEngine := engines.NewMaskingEngine(qc.service.GetDB())
		needMasking, err := maskingEngine.CheckMaskingRules(id, req.DBName, req.TbName)
		if err != nil {
			qc.log.Errorf("检查脱敏规则失败: %v", err)
		} else if needMasking {
			hitRule = true
			maskedResult, err = maskingEngine.ApplyMasking(id, req.DBName, req.TbName, result)
			if err != nil {
				qc.log.Errorf("应用数据脱敏失败: %v", err)
			} else {
				masking = true
			}
		}
	}

	// 记录查询日志
	queryLog := &models.SqlQueryLog{
		InstanceID: id,
		DBName:     req.DBName,
		SchemaName: req.SchemaName,
		SqlLog:     req.SqlContent,
		EffectRow:  maskedResult.AffectedRows,
		CostTime:   maskedResult.QueryTime.String(),
		HitRule:    hitRule,
		Masking:    masking,
	}

	if _, err := qc.service.CreateSqlQueryLog(queryLog); err != nil {
		qc.log.Errorf("记录查询日志失败: %v", err)
	}

	// 转换查询结果格式并返回
	responseResult := &models.QueryResult{
		Columns:             maskedResult.Columns,
		Rows:                maskedResult.Rows,
		AffectedRows:        maskedResult.AffectedRows,
		QueryTime:           maskedResult.QueryTime.String(),
		SecondsBehindMaster: maskedResult.SecondsBehindMaster,
	}

	qc.SuccessResponse(c, responseResult)
}

// ExplainQuery 获取SQL执行计划
// @Summary 获取SQL执行计划
// @Description 获取SQL语句的执行计划
// @Tags SQL查询
// @Accept json
// @Produce json
// @Param id path int true "实例ID"
// @Param query body models.SqlQueryRequest true "查询请求"
// @Success 200 {object} models.APIResponse{data=models.QueryResult}
// @Router /instance/{id}/explain [post]
func (qc *QueryController) ExplainQuery(c *gin.Context) {
	qc.LogRequest(c, "获取SQL执行计划")

	id, err := qc.GetIDParam(c)
	if err != nil {
		qc.BadRequestResponse(c, "无效的实例ID")
		return
	}

	var req models.SqlQueryRequest
	if !qc.ValidateJSON(c, &req) {
		return
	}

	// 检查实例是否存在
	instance, err := qc.service.GetDBInstanceByID(id)
	if err != nil {
		qc.NotFoundResponse(c, "数据库实例不存在")
		return
	}

	// 检查用户权限
	username := qc.GetCurrentUser(c)
	isAdmin := qc.IsAdmin(c)
	if !isAdmin && !qc.service.CheckUserQueryPrivilege(username, id, req.DBName) {
		qc.ErrorResponse(c, 403, "没有查询权限")
		return
	}

	// 在SQL前添加EXPLAIN
	explainSQL := "EXPLAIN " + req.SqlContent

	// 使用SQL引擎执行EXPLAIN查询
	engine, err := engines.GetEngine(instance)
	if err != nil {
		qc.log.Errorf("获取数据库引擎失败: %v", err)
		qc.InternalErrorResponse(c, "获取数据库引擎失败")
		return
	}
	defer engine.Close()

	// 执行EXPLAIN查询
	result, err := engine.Query(req.DBName, explainSQL, 0, req.SchemaName, req.TbName, 60)
	if err != nil {
		qc.log.Errorf("执行EXPLAIN查询失败: %v", err)
		qc.InternalErrorResponse(c, "执行EXPLAIN查询失败: "+err.Error())
		return
	}

	// 如果查询有错误，返回错误信息
	if result.Error != "" {
		qc.ErrorResponse(c, 400, "EXPLAIN执行错误: "+result.Error)
		return
	}

	// 记录查询日志
	queryLog := &models.SqlQueryLog{
		InstanceID: id,
		DBName:     req.DBName,
		SchemaName: req.SchemaName,
		SqlLog:     explainSQL,
		EffectRow:  result.AffectedRows,
		CostTime:   result.QueryTime.String(),
		HitRule:    false,
		Masking:    false,
	}

	if _, err := qc.service.CreateSqlQueryLog(queryLog); err != nil {
		qc.log.Errorf("记录查询日志失败: %v", err)
	}

	// 转换查询结果格式并返回
	responseResult := &models.QueryResult{
		Columns:      result.Columns,
		Rows:         result.Rows,
		AffectedRows: result.AffectedRows,
		QueryTime:    result.QueryTime.String(),
	}

	qc.SuccessResponse(c, responseResult)
}

// GetQueryLogs 获取查询日志列表
// @Summary 获取查询日志列表
// @Description 获取SQL查询日志列表，支持分页和搜索
// @Tags SQL查询
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Param instance_id query int false "实例ID"
// @Success 200 {object} models.APIResponse{data=models.PaginatedResponse}
// @Router /querylog [get]
func (qc *QueryController) GetQueryLogs(c *gin.Context) {
	qc.LogRequest(c, "获取查询日志列表")

	page, pageSize := qc.GetPaginationParams(c)
	search := qc.GetSearchParam(c)

	var instanceID uint
	if instanceIDStr := c.Query("instance_id"); instanceIDStr != "" {
		if id, err := qc.parseUint(instanceIDStr); err == nil {
			instanceID = id
		}
	}

	logs, total, err := qc.service.GetSqlQueryLogs(page, pageSize, search, instanceID)
	if err != nil {
		qc.log.Errorf("获取查询日志列表失败: %v", err)
		qc.InternalErrorResponse(c, "获取查询日志列表失败")
		return
	}

	qc.PaginatedResponse(c, logs, total, page, pageSize)
}

// GetQueryLog 获取查询日志详情
// @Summary 获取查询日志详情
// @Description 根据ID获取查询日志详情
// @Tags SQL查询
// @Accept json
// @Produce json
// @Param id path int true "日志ID"
// @Success 200 {object} models.APIResponse{data=models.SqlQueryLog}
// @Router /querylog/{id} [get]
func (qc *QueryController) GetQueryLog(c *gin.Context) {
	qc.LogRequest(c, "获取查询日志详情")

	id, err := qc.GetIDParam(c)
	if err != nil {
		qc.BadRequestResponse(c, "无效的日志ID")
		return
	}

	log, err := qc.service.GetSqlQueryLogByID(id)
	if err != nil {
		qc.log.Errorf("获取查询日志详情失败: %v", err)
		qc.NotFoundResponse(c, "查询日志不存在")
		return
	}

	qc.SuccessResponse(c, log)
}

// GetTableInfo 获取表信息
// @Summary 获取表信息
// @Description 获取数据库表的元信息、结构、索引等
// @Tags SQL查询
// @Accept json
// @Produce json
// @Param id path int true "实例ID"
// @Param db_name query string true "数据库名"
// @Param table_name query string true "表名"
// @Param schema_name query string false "模式名"
// @Success 200 {object} models.APIResponse
// @Router /instance/{id}/table/info [get]
func (qc *QueryController) GetTableInfo(c *gin.Context) {
	qc.LogRequest(c, "获取表信息")

	id, err := qc.GetIDParam(c)
	if err != nil {
		qc.BadRequestResponse(c, "无效的实例ID")
		return
	}

	dbName := c.Query("db_name")
	tableName := c.Query("table_name")
	schemaName := c.Query("schema_name")

	if dbName == "" || tableName == "" {
		qc.BadRequestResponse(c, "数据库名和表名不能为空")
		return
	}

	// 检查实例是否存在
	instance, err := qc.service.GetDBInstanceByID(id)
	if err != nil {
		qc.NotFoundResponse(c, "数据库实例不存在")
		return
	}

	// 使用SQL引擎获取表信息
	engine, err := engines.GetEngine(instance)
	if err != nil {
		qc.log.Errorf("获取数据库引擎失败: %v", err)
		qc.InternalErrorResponse(c, "获取数据库引擎失败")
		return
	}
	defer engine.Close()

	// 获取表的各种信息
	metaData := engine.GetTableMetaData(dbName, tableName)
	descData := engine.GetTableDescData(dbName, tableName, schemaName)
	indexData := engine.GetTableIndexData(dbName, tableName)

	// 获取建表语句
	createResult, err := engine.GetTableCreate(dbName, tableName, schemaName)
	var createSQL [][]interface{}
	if err == nil && len(createResult.Rows) > 0 {
		createSQL = createResult.Rows
	} else {
		createSQL = [][]interface{}{{tableName, "-- 无法获取建表语句"}}
	}

	tableInfo := map[string]interface{}{
		"meta_data":  metaData,
		"desc":       descData,
		"index":      indexData,
		"create_sql": createSQL,
	}

	qc.SuccessResponse(c, tableInfo)
}

// parseUint 解析uint参数
func (qc *QueryController) parseUint(s string) (uint, error) {
	if s == "" {
		return 0, nil
	}
	id, err := strconv.ParseUint(s, 10, 32)
	if err != nil {
		return 0, err
	}
	return uint(id), nil
}
