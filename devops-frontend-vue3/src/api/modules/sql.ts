import request from '@/utils/request'

// 数据库实例接口
export interface DBInstance {
  id?: number
  instance_name: string
  alias: string
  type: 'master' | 'slave'
  db_type: 'mysql' | 'postgres' | 'sqlite' | 'mongodb' | 'redis' | 'oracle' | 'mssql' | 'clickhouse'
  mode: 'standalone' | 'cluster'
  host: string
  port: number
  user: string
  password: string
  db_name: string
  charset: string
  auto_backup: number
  projects: any[]
  environments: any[]
  is_deleted: number
  created_at?: string
  updated_at?: string
}

// SQL查询请求接口
export interface SqlQueryRequest {
  sql_content: string
  db_name: string
  limit_num?: number
  schema_name?: string
  tb_name?: string
}

// 查询结果接口
export interface QueryResult {
  column_list: string[]
  rows: any[][]
  affected_rows: number
  query_time: number
  error?: string
}

// 表信息接口
export interface TableInfo {
  table_name: string
  table_comment: string
  columns: ColumnInfo[]
  indexes: IndexInfo[]
}

export interface ColumnInfo {
  column_name: string
  data_type: string
  is_nullable: string
  column_default: string
  column_comment: string
  character_maximum_length?: number
}

export interface IndexInfo {
  index_name: string
  column_name: string
  index_type: string
  is_unique: boolean
}

// 数据库列表接口
export interface DatabaseInfo {
  database_name: string
  charset: string
  collation: string
}

// API响应接口
export interface APIResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface PaginatedResponse<T> {
  code: number
  message: string
  data: {
    items: T[]
    total: number
    page: number
    page_size: number
  }
}

// SQL服务API
export const sqlApi = {
  // 获取数据库实例列表
  getDBInstances: (params?: { page?: number; page_size?: number; instance_name?: string }) => {
    return request.get<PaginatedResponse<DBInstance>>('/v1/sql/instance', { params })
  },

  // 获取单个数据库实例
  getDBInstance: (id: number) => {
    return request.get<APIResponse<DBInstance>>(`/v1/sql/instance/${id}`)
  },

  // 创建数据库实例
  createDBInstance: (data: Omit<DBInstance, 'id' | 'created_at' | 'updated_at'>) => {
    return request.post<APIResponse<DBInstance>>('/v1/sql/instance', data)
  },

  // 更新数据库实例
  updateDBInstance: (id: number, data: Partial<DBInstance>) => {
    return request.put<APIResponse<DBInstance>>(`/v1/sql/instance/${id}`, data)
  },

  // 删除数据库实例
  deleteDBInstance: (id: number) => {
    return request.delete<APIResponse<void>>(`/v1/sql/instance/${id}`)
  },

  // 执行SQL查询
  executeQuery: (instanceId: number, data: SqlQueryRequest) => {
    return request.post<APIResponse<QueryResult>>(`/v1/sql/instance/${instanceId}/query`, data)
  },

  // 获取SQL执行计划
  explainQuery: (instanceId: number, data: SqlQueryRequest) => {
    return request.post<APIResponse<QueryResult>>(`/v1/sql/instance/${instanceId}/explain`, data)
  },

  // 获取数据库列表
  getDatabases: (instanceId: number) => {
    return request.get<APIResponse<DatabaseInfo[]>>(`/v1/sql/instance/${instanceId}/databases`)
  },

  // 获取表列表
  getTables: (instanceId: number, params: { db_name: string; schema_name?: string }) => {
    return request.get<APIResponse<string[]>>(`/v1/sql/instance/${instanceId}/tables`, { params })
  },

  // 获取表信息
  getTableInfo: (instanceId: number, params: { db_name: string; table_name: string; schema_name?: string }) => {
    return request.get<APIResponse<TableInfo>>(`/v1/sql/instance/${instanceId}/table/info`, { params })
  },

  // 获取实例资源信息
  getInstanceResource: (instanceId: number) => {
    return request.get<APIResponse<any>>(`/v1/sql/instance/${instanceId}/resource`)
  },

  // 下载查询结果
  downloadQueryResult: (params: { query_id: string; format?: 'csv' | 'xlsx' }) => {
    return request.get('/v1/sql/download', {
      params,
      responseType: 'blob'
    })
  }
}

export default sqlApi
