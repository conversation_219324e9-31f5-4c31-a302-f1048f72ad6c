package controllers

import (
	"github.com/devops-microservices/sql-service/models"
	"github.com/devops-microservices/sql-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// WorkflowController SQL工作流控制器
type WorkflowController struct {
	*BaseController
	service services.SqlService
}

// NewWorkflowController 创建SQL工作流控制器
func NewWorkflowController(service services.SqlService, log *logrus.Logger) *WorkflowController {
	return &WorkflowController{
		BaseController: NewBaseController(log),
		service:        service,
	}
}

// GetWorkflows 获取SQL工单列表
// @Summary 获取SQL工单列表
// @Description 获取SQL工单列表，支持分页和搜索
// @Tags SQL工作流
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Param status query string false "工单状态"
// @Success 200 {object} models.APIResponse{data=models.PaginatedResponse}
// @Router /workflow [get]
func (wc *WorkflowController) GetWorkflows(c *gin.Context) {
	wc.LogRequest(c, "获取SQL工单列表")

	page, pageSize := wc.GetPaginationParams(c)
	search := wc.GetSearchParam(c)
	status := c.Query("status")

	workflows, total, err := wc.service.GetSqlWorkflows(page, pageSize, search, status)
	if err != nil {
		wc.log.Errorf("获取SQL工单列表失败: %v", err)
		wc.InternalErrorResponse(c, "获取SQL工单列表失败")
		return
	}

	wc.PaginatedResponse(c, workflows, total, page, pageSize)
}

// GetWorkflow 获取SQL工单详情
// @Summary 获取SQL工单详情
// @Description 根据ID获取SQL工单详情
// @Tags SQL工作流
// @Accept json
// @Produce json
// @Param id path int true "工单ID"
// @Success 200 {object} models.APIResponse{data=models.SqlWorkflow}
// @Router /workflow/{id} [get]
func (wc *WorkflowController) GetWorkflow(c *gin.Context) {
	wc.LogRequest(c, "获取SQL工单详情")

	id, err := wc.GetIDParam(c)
	if err != nil {
		wc.BadRequestResponse(c, "无效的工单ID")
		return
	}

	workflow, err := wc.service.GetSqlWorkflowByID(id)
	if err != nil {
		wc.log.Errorf("获取SQL工单详情失败: %v", err)
		wc.NotFoundResponse(c, "SQL工单不存在")
		return
	}

	wc.SuccessResponse(c, workflow)
}

// GetWorkflowByOrderID 根据工单号获取SQL工单详情
// @Summary 根据工单号获取SQL工单详情
// @Description 根据工单号获取SQL工单详情
// @Tags SQL工作流
// @Accept json
// @Produce json
// @Param order_id query string true "工单号"
// @Success 200 {object} models.APIResponse{data=models.SqlWorkflow}
// @Router /workflow/detail [get]
func (wc *WorkflowController) GetWorkflowByOrderID(c *gin.Context) {
	wc.LogRequest(c, "根据工单号获取SQL工单详情")

	orderID := c.Query("order_id")
	if orderID == "" {
		wc.BadRequestResponse(c, "工单号不能为空")
		return
	}

	workflow, err := wc.service.GetSqlWorkflowByOrderID(orderID)
	if err != nil {
		wc.log.Errorf("获取SQL工单详情失败: %v", err)
		wc.NotFoundResponse(c, "SQL工单不存在")
		return
	}

	wc.SuccessResponse(c, workflow)
}

// CreateWorkflow 创建SQL工单
// @Summary 创建SQL工单
// @Description 创建新的SQL工单
// @Tags SQL工作流
// @Accept json
// @Produce json
// @Param workflow body models.SqlWorkflowCreateRequest true "工单信息"
// @Success 200 {object} models.APIResponse{data=models.SqlWorkflow}
// @Router /workflow [post]
func (wc *WorkflowController) CreateWorkflow(c *gin.Context) {
	wc.LogRequest(c, "创建SQL工单")

	var req models.SqlWorkflowCreateRequest
	if !wc.ValidateJSON(c, &req) {
		return
	}

	username := wc.GetCurrentUser(c)

	workflow, err := wc.service.CreateSqlWorkflow(&req, username)
	if err != nil {
		wc.log.Errorf("创建SQL工单失败: %v", err)
		wc.InternalErrorResponse(c, "创建SQL工单失败: "+err.Error())
		return
	}

	wc.SuccessResponse(c, workflow)
}

// UpdateWorkflow 更新SQL工单
// @Summary 更新SQL工单
// @Description 更新SQL工单信息
// @Tags SQL工作流
// @Accept json
// @Produce json
// @Param id path int true "工单ID"
// @Param workflow body models.SqlWorkflowCreateRequest true "工单信息"
// @Success 200 {object} models.APIResponse{data=models.SqlWorkflow}
// @Router /workflow/{id} [put]
func (wc *WorkflowController) UpdateWorkflow(c *gin.Context) {
	wc.LogRequest(c, "更新SQL工单")

	id, err := wc.GetIDParam(c)
	if err != nil {
		wc.BadRequestResponse(c, "无效的工单ID")
		return
	}

	var req models.SqlWorkflowCreateRequest
	if !wc.ValidateJSON(c, &req) {
		return
	}

	updates := map[string]interface{}{
		"title":        req.Title,
		"db_name":      req.DBName,
		"schema_name":  req.SchemaName,
		"is_backup":    req.IsBackup,
		"sql_content":  req.SqlContent,
		"method":       req.Method,
		"expect_time":  req.ExpectTime,
		"related_oid":  req.RelatedOID,
	}

	workflow, err := wc.service.UpdateSqlWorkflow(id, updates)
	if err != nil {
		wc.log.Errorf("更新SQL工单失败: %v", err)
		wc.InternalErrorResponse(c, "更新SQL工单失败: "+err.Error())
		return
	}

	wc.SuccessResponse(c, workflow)
}

// DeleteWorkflow 删除SQL工单
// @Summary 删除SQL工单
// @Description 删除SQL工单
// @Tags SQL工作流
// @Accept json
// @Produce json
// @Param id path int true "工单ID"
// @Success 200 {object} models.APIResponse
// @Router /workflow/{id} [delete]
func (wc *WorkflowController) DeleteWorkflow(c *gin.Context) {
	wc.LogRequest(c, "删除SQL工单")

	id, err := wc.GetIDParam(c)
	if err != nil {
		wc.BadRequestResponse(c, "无效的工单ID")
		return
	}

	if err := wc.service.DeleteSqlWorkflow(id); err != nil {
		wc.log.Errorf("删除SQL工单失败: %v", err)
		wc.InternalErrorResponse(c, "删除SQL工单失败: "+err.Error())
		return
	}

	wc.SuccessResponse(c, "删除成功")
}

// ExecuteWorkflow 执行SQL工单
// @Summary 执行SQL工单
// @Description 执行SQL工单中的SQL语句
// @Tags SQL工作流
// @Accept json
// @Produce json
// @Param id path int true "工单ID"
// @Success 200 {object} models.APIResponse
// @Router /workflow/{id}/execute [post]
func (wc *WorkflowController) ExecuteWorkflow(c *gin.Context) {
	wc.LogRequest(c, "执行SQL工单")

	id, err := wc.GetIDParam(c)
	if err != nil {
		wc.BadRequestResponse(c, "无效的工单ID")
		return
	}

	username := wc.GetCurrentUser(c)

	if err := wc.service.ExecuteSqlWorkflow(id, username); err != nil {
		wc.log.Errorf("执行SQL工单失败: %v", err)
		wc.InternalErrorResponse(c, "执行SQL工单失败: "+err.Error())
		return
	}

	wc.SuccessResponse(c, "工单已提交执行")
}

// CheckSQL 检查SQL语句
// @Summary 检查SQL语句
// @Description 检查SQL语句的语法和安全性
// @Tags SQL工作流
// @Accept json
// @Produce json
// @Param id path int true "实例ID"
// @Param request body map[string]interface{} true "检查请求"
// @Success 200 {object} models.APIResponse{data=[]models.CheckResult}
// @Router /instance/{id}/check [post]
func (wc *WorkflowController) CheckSQL(c *gin.Context) {
	wc.LogRequest(c, "检查SQL语句")

	id, err := wc.GetIDParam(c)
	if err != nil {
		wc.BadRequestResponse(c, "无效的实例ID")
		return
	}

	var req map[string]interface{}
	if !wc.ValidateJSON(c, &req) {
		return
	}

	dbName, ok := req["db_name"].(string)
	if !ok || dbName == "" {
		wc.BadRequestResponse(c, "数据库名不能为空")
		return
	}

	fullSQL, ok := req["full_sql"].(string)
	if !ok || fullSQL == "" {
		wc.BadRequestResponse(c, "SQL语句不能为空")
		return
	}

	// 检查实例是否存在
	instance, err := wc.service.GetDBInstanceByID(id)
	if err != nil {
		wc.NotFoundResponse(c, "数据库实例不存在")
		return
	}

	// TODO: 实现实际的SQL检查逻辑
	// 这里应该使用SQL引擎来检查SQL语句

	// 模拟检查结果
	checkResults := []models.CheckResult{
		{
			ID:           1,
			Stage:        "CHECKED",
			ErrLevel:     0,
			StageStatus:  "Audit completed",
			ErrorMessage: "",
			SQL:          fullSQL,
			AffectedRows: 0,
			Sequence:     "1",
			BackupDBName: "",
			ExecuteTime:  "0",
			SqlSha1:      "",
			BackupTime:   "",
		},
	}

	wc.SuccessResponse(c, map[string]interface{}{
		"column_list": []string{"ID", "stage", "errlevel", "stagestatus", "errormessage", "SQL", "Affected_rows", "sequence", "backup_dbname", "execute_time", "sqlsha1", "backup_time"},
		"rows":        checkResults,
		"warning":     nil,
		"error":       nil,
		"checked":     true,
	})
}

// GetWorkflowCount 获取待处理工单数量
// @Summary 获取待处理工单数量
// @Description 获取当前用户待处理的工单数量
// @Tags SQL工作流
// @Accept json
// @Produce json
// @Success 200 {object} models.APIResponse{data=int}
// @Router /workflow/count [get]
func (wc *WorkflowController) GetWorkflowCount(c *gin.Context) {
	wc.LogRequest(c, "获取待处理工单数量")

	// TODO: 实现获取待处理工单数量的逻辑
	count := 5 // 示例数据

	wc.SuccessResponse(c, count)
}
