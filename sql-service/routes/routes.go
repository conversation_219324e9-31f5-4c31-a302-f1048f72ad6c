package routes

import (
	"net/http"

	"github.com/devops-microservices/sql-service/config"
	"github.com/devops-microservices/sql-service/controllers"
	"github.com/devops-microservices/sql-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/gorm"
)

// SetupRoutes 设置所有路由
func SetupRoutes(router *gin.Engine, db *gorm.DB, cfg *config.Config, log *logrus.Logger) {
	// 初始化服务
	sqlService := services.NewSqlService(db, log)

	// 初始化控制器
	instanceController := controllers.NewInstanceController(sqlService, log)
	queryController := controllers.NewQueryController(sqlService, log)
	workflowController := controllers.NewWorkflowController(sqlService, log)

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"service": "sql-service",
			"version": "1.0.0",
		})
	})

	// Swagger文档
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API路由组
	api := router.Group("/api/sql")
	{
		// 数据库实例管理路由
		instanceRoutes := api.Group("/instance")
		{
			instanceRoutes.GET("", instanceController.GetInstances)
			instanceRoutes.POST("", instanceController.CreateInstance)
			instanceRoutes.GET("/:id", instanceController.GetInstance)
			instanceRoutes.PUT("/:id", instanceController.UpdateInstance)
			instanceRoutes.DELETE("/:id", instanceController.DeleteInstance)
			
			// 用户实例相关
			instanceRoutes.GET("/user/instances", instanceController.GetUserInstances)
			
			// 实例操作
			instanceRoutes.POST("/:id/test", instanceController.TestConnection)
			instanceRoutes.GET("/:id/databases", instanceController.GetDatabases)
			
			// SQL查询相关
			instanceRoutes.POST("/:id/query", queryController.ExecuteQuery)
			instanceRoutes.POST("/:id/explain", queryController.ExplainQuery)
			instanceRoutes.GET("/:id/table/info", queryController.GetTableInfo)
			
			// SQL检查
			instanceRoutes.POST("/:id/check", workflowController.CheckSQL)
		}

		// SQL工作流路由
		workflowRoutes := api.Group("/workflow")
		{
			workflowRoutes.GET("", workflowController.GetWorkflows)
			workflowRoutes.POST("", workflowController.CreateWorkflow)
			workflowRoutes.GET("/detail", workflowController.GetWorkflowByOrderID)
			workflowRoutes.GET("/count", workflowController.GetWorkflowCount)
			workflowRoutes.GET("/:id", workflowController.GetWorkflow)
			workflowRoutes.PUT("/:id", workflowController.UpdateWorkflow)
			workflowRoutes.DELETE("/:id", workflowController.DeleteWorkflow)
			workflowRoutes.POST("/:id/execute", workflowController.ExecuteWorkflow)
		}

		// 查询日志路由
		queryLogRoutes := api.Group("/querylog")
		{
			queryLogRoutes.GET("", queryController.GetQueryLogs)
			queryLogRoutes.GET("/:id", queryController.GetQueryLog)
		}

		// 数据脱敏路由
		dataMaskRoutes := api.Group("/datamask")
		{
			dataMaskRoutes.GET("", getDataMaskingColumns)
			dataMaskRoutes.POST("", createDataMaskingColumns)
			dataMaskRoutes.GET("/:id", getDataMaskingColumn)
			dataMaskRoutes.PUT("/:id", updateDataMaskingColumns)
			dataMaskRoutes.DELETE("/:id", deleteDataMaskingColumns)
		}

		// 查询权限路由
		privilegeRoutes := api.Group("/privilege")
		{
			privilegeRoutes.GET("", getQueryPrivileges)
			privilegeRoutes.POST("", createQueryPrivileges)
			privilegeRoutes.GET("/:id", getQueryPrivilege)
			privilegeRoutes.PUT("/:id", updateQueryPrivileges)
			privilegeRoutes.DELETE("/:id", deleteQueryPrivileges)
		}

		// 下载路由
		api.GET("/download", downloadQueryResult)
	}

	// 打印路由信息
	printRoutes(log)
}

// 数据脱敏相关处理器（临时实现）
func getDataMaskingColumns(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data": gin.H{
			"items":       []interface{}{},
			"total":       0,
			"page":        1,
			"page_size":   20,
			"total_pages": 0,
		},
	})
}

func createDataMaskingColumns(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    gin.H{},
	})
}

func getDataMaskingColumn(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    gin.H{},
	})
}

func updateDataMaskingColumns(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    gin.H{},
	})
}

func deleteDataMaskingColumns(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// 查询权限相关处理器（临时实现）
func getQueryPrivileges(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data": gin.H{
			"items":       []interface{}{},
			"total":       0,
			"page":        1,
			"page_size":   20,
			"total_pages": 0,
		},
	})
}

func createQueryPrivileges(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    gin.H{},
	})
}

func getQueryPrivilege(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    gin.H{},
	})
}

func updateQueryPrivileges(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    gin.H{},
	})
}

func deleteQueryPrivileges(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// 下载查询结果处理器（临时实现）
func downloadQueryResult(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "下载功能暂未实现",
	})
}

// printRoutes 打印路由信息
func printRoutes(log *logrus.Logger) {
	log.Info("📋 API路由注册完成:")
	log.Info("  ├── GET    /health                          - 健康检查")
	log.Info("  ├── GET    /swagger/*any                    - API文档")
	log.Info("  └── /api/sql")
	log.Info("      ├── /instance")
	log.Info("      │   ├── GET    /                        - 获取实例列表")
	log.Info("      │   ├── POST   /                        - 创建实例")
	log.Info("      │   ├── GET    /:id                     - 获取实例详情")
	log.Info("      │   ├── PUT    /:id                     - 更新实例")
	log.Info("      │   ├── DELETE /:id                     - 删除实例")
	log.Info("      │   ├── GET    /user/instances          - 获取用户实例")
	log.Info("      │   ├── POST   /:id/test                - 测试连接")
	log.Info("      │   ├── GET    /:id/databases           - 获取数据库列表")
	log.Info("      │   ├── POST   /:id/query               - 执行查询")
	log.Info("      │   ├── POST   /:id/explain             - 执行计划")
	log.Info("      │   ├── GET    /:id/table/info          - 获取表信息")
	log.Info("      │   └── POST   /:id/check               - 检查SQL")
	log.Info("      ├── /workflow")
	log.Info("      │   ├── GET    /                        - 获取工单列表")
	log.Info("      │   ├── POST   /                        - 创建工单")
	log.Info("      │   ├── GET    /detail                  - 根据工单号获取详情")
	log.Info("      │   ├── GET    /count                   - 获取工单数量")
	log.Info("      │   ├── GET    /:id                     - 获取工单详情")
	log.Info("      │   ├── PUT    /:id                     - 更新工单")
	log.Info("      │   ├── DELETE /:id                     - 删除工单")
	log.Info("      │   └── POST   /:id/execute             - 执行工单")
	log.Info("      ├── /querylog")
	log.Info("      │   ├── GET    /                        - 获取查询日志")
	log.Info("      │   └── GET    /:id                     - 获取日志详情")
	log.Info("      ├── /datamask")
	log.Info("      │   ├── GET    /                        - 获取脱敏配置")
	log.Info("      │   ├── POST   /                        - 创建脱敏配置")
	log.Info("      │   ├── GET    /:id                     - 获取脱敏详情")
	log.Info("      │   ├── PUT    /:id                     - 更新脱敏配置")
	log.Info("      │   └── DELETE /:id                     - 删除脱敏配置")
	log.Info("      ├── /privilege")
	log.Info("      │   ├── GET    /                        - 获取查询权限")
	log.Info("      │   ├── POST   /                        - 创建查询权限")
	log.Info("      │   ├── GET    /:id                     - 获取权限详情")
	log.Info("      │   ├── PUT    /:id                     - 更新查询权限")
	log.Info("      │   └── DELETE /:id                     - 删除查询权限")
	log.Info("      └── GET    /download                    - 下载查询结果")
	log.Info("")
	log.Info("🔗 总计路由数: 32")
}
