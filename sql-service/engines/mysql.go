package engines

import (
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/devops-microservices/sql-service/config"
	"github.com/devops-microservices/sql-service/models"
	_ "github.com/go-sql-driver/mysql"
)

// MySQLEngine MySQL数据库引擎
type MySQLEngine struct {
	*BaseEngine
	goInception *GoInceptionEngine
}

// NewMySQLEngine 创建MySQL引擎
func NewMySQLEngine(instance *models.DBInstance) *MySQLEngine {
	return &MySQLEngine{
		BaseEngine: NewBaseEngine(instance),
	}
}

// NewMySQLEngineWithGoInception 创建带GoInception的MySQL引擎
func NewMySQLEngineWithGoInception(instance *models.DBInstance, goInceptionCfg *config.GoInceptionConfig) *MySQLEngine {
	var goInception *GoInceptionEngine
	if goInceptionCfg != nil && goInceptionCfg.Enabled {
		goInception = NewGoInceptionEngine(goInceptionCfg, instance)
	}

	return &MySQLEngine{
		BaseEngine:  NewBaseEngine(instance),
		goInception: goInception,
	}
}

// GetConnection 获取数据库连接
func (e *MySQLEngine) GetConnection(dbName string) (*sql.DB, error) {
	if e.Connection != nil {
		return e.Connection, nil
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		e.Instance.User, e.Instance.Password, e.Instance.Host, e.Instance.Port, dbName, e.Instance.Charset)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, err
	}

	if err := db.Ping(); err != nil {
		return nil, err
	}

	e.Connection = db
	return db, nil
}

// TestConnection 测试连接
func (e *MySQLEngine) TestConnection() error {
	db, err := e.GetConnection(e.Instance.DBName)
	if err != nil {
		return err
	}
	return db.Ping()
}

// Close 关闭连接
func (e *MySQLEngine) Close() error {
	if e.Connection != nil {
		return e.Connection.Close()
	}
	return nil
}

// Query 执行查询
func (e *MySQLEngine) Query(dbName, sql string, limitNum int, schemaName, tbName string, maxExecutionTime int) (*QueryResult, error) {
	db, err := e.GetConnection(dbName)
	if err != nil {
		return nil, err
	}

	// 添加LIMIT限制
	if limitNum > 0 && !strings.Contains(strings.ToUpper(sql), "LIMIT") {
		sql = BuildLimitSQL(sql, limitNum, "mysql")
	}

	start := time.Now()
	rows, err := db.Query(sql)
	if err != nil {
		return &QueryResult{
			Error:     FormatError(err),
			QueryTime: time.Since(start),
		}, nil
	}
	defer rows.Close()

	data, columns, err := ConvertRowsToInterface(rows)
	if err != nil {
		return &QueryResult{
			Error:     FormatError(err),
			QueryTime: time.Since(start),
		}, nil
	}

	return &QueryResult{
		Columns:             columns,
		Rows:                data,
		AffectedRows:        int64(len(data)),
		QueryTime:           time.Since(start),
		SecondsBehindMaster: e.GetSecondsBehindMaster(),
	}, nil
}

// QueryCheck 查询检查
func (e *MySQLEngine) QueryCheck(dbName, sql string) map[string]interface{} {
	result := map[string]interface{}{
		"bad_query":    false,
		"has_star":     false,
		"filtered_sql": sql,
		"msg":          "",
	}

	// 检查是否包含SELECT *
	if strings.Contains(strings.ToUpper(sql), "SELECT *") {
		result["has_star"] = true
		result["msg"] = "查询语句包含SELECT *，可能影响性能"
	}

	// 检查危险操作
	dangerousKeywords := []string{"DROP", "DELETE", "TRUNCATE", "ALTER"}
	upperSQL := strings.ToUpper(sql)
	for _, keyword := range dangerousKeywords {
		if strings.Contains(upperSQL, keyword) {
			result["bad_query"] = true
			result["msg"] = fmt.Sprintf("查询语句包含危险操作: %s", keyword)
			break
		}
	}

	return result
}

// FilterSQL 过滤SQL语句
func (e *MySQLEngine) FilterSQL(sql string, limitNum int) string {
	return BuildLimitSQL(sql, limitNum, "mysql")
}

// GetAllDatabases 获取所有数据库
func (e *MySQLEngine) GetAllDatabases() (*QueryResult, error) {
	return e.Query("", "SHOW DATABASES", 0, "", "", 0)
}

// GetAllSchemas 获取所有模式（MySQL中等同于数据库）
func (e *MySQLEngine) GetAllSchemas(dbName string) (*QueryResult, error) {
	return e.Query(dbName, "SHOW DATABASES", 0, "", "", 0)
}

// GetAllTables 获取所有表
func (e *MySQLEngine) GetAllTables(dbName, schemaName string) (*QueryResult, error) {
	sql := "SHOW TABLES"
	return e.Query(dbName, sql, 0, "", "", 0)
}

// GetAllColumnsByTable 获取表的所有列
func (e *MySQLEngine) GetAllColumnsByTable(dbName, tbName, schemaName string) (*QueryResult, error) {
	sql := fmt.Sprintf("SHOW COLUMNS FROM `%s`", tbName)
	return e.Query(dbName, sql, 0, "", "", 0)
}

// GetTableMetaData 获取表元数据
func (e *MySQLEngine) GetTableMetaData(dbName, tbName string) map[string]interface{} {
	sql := fmt.Sprintf("SELECT table_name, engine, table_collation, table_rows FROM information_schema.tables WHERE table_schema='%s' AND table_name='%s'", dbName, tbName)
	result, err := e.Query(dbName, sql, 0, "", "", 0)
	if err != nil || len(result.Rows) == 0 {
		return map[string]interface{}{"rows": []interface{}{tbName, "", "", 0}}
	}
	return map[string]interface{}{"rows": result.Rows[0]}
}

// GetTableDescData 获取表结构数据
func (e *MySQLEngine) GetTableDescData(dbName, tbName, schemaName string) map[string]interface{} {
	result, err := e.GetAllColumnsByTable(dbName, tbName, schemaName)
	if err != nil {
		return map[string]interface{}{"error": err.Error()}
	}
	return map[string]interface{}{
		"column_list": result.Columns,
		"rows":        result.Rows,
	}
}

// GetTableIndexData 获取表索引数据
func (e *MySQLEngine) GetTableIndexData(dbName, tbName string) map[string]interface{} {
	sql := fmt.Sprintf("SHOW INDEX FROM `%s`", tbName)
	result, err := e.Query(dbName, sql, 0, "", "", 0)
	if err != nil {
		return map[string]interface{}{"error": err.Error()}
	}
	return map[string]interface{}{
		"column_list": result.Columns,
		"rows":        result.Rows,
	}
}

// GetTableCreate 获取建表语句
func (e *MySQLEngine) GetTableCreate(dbName, tbName, schemaName string) (*QueryResult, error) {
	sql := fmt.Sprintf("SHOW CREATE TABLE `%s`", tbName)
	return e.Query(dbName, sql, 0, "", "", 0)
}

// DescribeTable 描述表结构
func (e *MySQLEngine) DescribeTable(dbName, tbName, schemaName string) (*QueryResult, error) {
	sql := fmt.Sprintf("DESCRIBE `%s`", tbName)
	return e.Query(dbName, sql, 0, "", "", 0)
}

// ExecuteCheck 执行检查
func (e *MySQLEngine) ExecuteCheck(dbName, sql string) (*ReviewSet, error) {
	// 如果启用了GoInception，使用GoInception进行检查
	if e.goInception != nil {
		return e.goInception.CheckSQL(dbName, sql)
	}

	// 基础SQL语法检查
	if err := ValidateSQL(sql); err != nil {
		return &ReviewSet{
			FullSQL:      sql,
			IsExecute:    false,
			Checked:      &[]bool{true}[0],
			WarningCount: 0,
			ErrorCount:   1,
			IsCritical:   true,
			SyntaxType:   ParseSQLType(sql),
			Error:        &[]string{err.Error()}[0],
			Rows: []ReviewResult{
				{
					ID:           1,
					Stage:        "CHECKED",
					ErrLevel:     2,
					StageStatus:  "Audit failed",
					ErrorMessage: err.Error(),
					SQL:          sql,
					AffectedRows: 0,
					Sequence:     "1",
				},
			},
			AffectedRows: 0,
		}, nil
	}

	// 执行EXPLAIN检查SQL性能
	db, err := e.GetConnection(dbName)
	if err != nil {
		return nil, err
	}

	explainSQL := "EXPLAIN " + sql
	rows, err := db.Query(explainSQL)
	if err != nil {
		// SQL语法错误
		return &ReviewSet{
			FullSQL:      sql,
			IsExecute:    false,
			Checked:      &[]bool{true}[0],
			WarningCount: 0,
			ErrorCount:   1,
			IsCritical:   true,
			SyntaxType:   ParseSQLType(sql),
			Error:        &[]string{err.Error()}[0],
			Rows: []ReviewResult{
				{
					ID:           1,
					Stage:        "CHECKED",
					ErrLevel:     2,
					StageStatus:  "Audit failed",
					ErrorMessage: err.Error(),
					SQL:          sql,
					AffectedRows: 0,
					Sequence:     "1",
				},
			},
			AffectedRows: 0,
		}, nil
	}
	defer rows.Close()

	// SQL检查通过
	reviewSet := &ReviewSet{
		FullSQL:      sql,
		IsExecute:    true,
		Checked:      &[]bool{true}[0],
		WarningCount: 0,
		ErrorCount:   0,
		IsCritical:   false,
		SyntaxType:   ParseSQLType(sql),
		Rows: []ReviewResult{
			{
				ID:           1,
				Stage:        "CHECKED",
				ErrLevel:     0,
				StageStatus:  "Audit completed",
				ErrorMessage: "",
				SQL:          sql,
				AffectedRows: 0,
				Sequence:     "1",
			},
		},
		AffectedRows: 0,
	}

	return reviewSet, nil
}

// Execute 执行SQL
func (e *MySQLEngine) Execute(dbName, sql string) (*ReviewSet, error) {
	// 如果启用了GoInception，使用GoInception进行执行
	if e.goInception != nil {
		return e.goInception.ExecuteSQL(dbName, sql)
	}

	// 先检查SQL
	reviewSet, err := e.ExecuteCheck(dbName, sql)
	if err != nil {
		return nil, err
	}

	// 如果检查失败，不执行
	if reviewSet.ErrorCount > 0 {
		return reviewSet, nil
	}

	// 执行SQL
	db, err := e.GetConnection(dbName)
	if err != nil {
		return nil, err
	}

	start := time.Now()
	result, err := db.Exec(sql)
	executeTime := time.Since(start)

	if err != nil {
		// 执行失败
		reviewSet.ErrorCount = 1
		reviewSet.IsCritical = true
		reviewSet.Error = &[]string{err.Error()}[0]
		reviewSet.Rows[0].ErrLevel = 2
		reviewSet.Rows[0].StageStatus = "Execute failed"
		reviewSet.Rows[0].ErrorMessage = err.Error()
		reviewSet.Rows[0].ExecuteTime = executeTime.String()
		return reviewSet, nil
	}

	// 执行成功
	affectedRows, _ := result.RowsAffected()
	reviewSet.AffectedRows = affectedRows
	reviewSet.Rows[0].AffectedRows = affectedRows
	reviewSet.Rows[0].StageStatus = "Execute Successfully"
	reviewSet.Rows[0].ExecuteTime = executeTime.String()

	return reviewSet, nil
}

// GetRollback 获取回滚语句
func (e *MySQLEngine) GetRollback(workflow *models.SqlWorkflow) ([][]string, error) {
	// 实现MySQL的回滚语句生成逻辑
	rollbackSQL, err := e.generateMySQLRollback(workflow.DBName, workflow.SqlContent)
	if err != nil {
		return nil, err
	}

	// 返回回滚语句列表
	return [][]string{{rollbackSQL}}, nil
}

// QueryMasking 查询脱敏
func (e *MySQLEngine) QueryMasking(dbName, sql string, queryResult *QueryResult) (*QueryResult, error) {
	// 这个方法需要在外部调用，因为需要数据库连接来获取脱敏配置
	// 实际的脱敏逻辑在MaskingEngine中实现
	return queryResult, nil
}

// GetSecondsBehindMaster 获取主从延迟
func (e *MySQLEngine) GetSecondsBehindMaster() int {
	if e.Instance.Type != "slave" {
		return 0
	}

	result, err := e.Query("", "SHOW SLAVE STATUS", 0, "", "", 0)
	if err != nil || len(result.Rows) == 0 {
		return 0
	}

	// 解析SHOW SLAVE STATUS结果获取Seconds_Behind_Master
	// Seconds_Behind_Master通常是第32个字段（索引31）
	for _, row := range result.Rows {
		if len(row) > 31 {
			if seconds, ok := row[31].(int); ok {
				return seconds
			}
			if secondsStr, ok := row[31].(string); ok {
				if secondsStr == "NULL" || secondsStr == "" {
					return 0
				}
				// 尝试转换为整数
				if seconds, err := strconv.Atoi(secondsStr); err == nil {
					return seconds
				}
			}
		}
	}
	return 0
}

// generateMySQLRollback 生成MySQL回滚SQL
func (e *MySQLEngine) generateMySQLRollback(dbName, sql string) (string, error) {
	sqlUpper := strings.ToUpper(strings.TrimSpace(sql))

	if strings.HasPrefix(sqlUpper, "INSERT") {
		return e.generateMySQLDeleteRollback(sql)
	} else if strings.HasPrefix(sqlUpper, "UPDATE") {
		return e.generateMySQLUpdateRollback(sql, dbName)
	} else if strings.HasPrefix(sqlUpper, "DELETE") {
		return e.generateMySQLInsertRollback(sql, dbName)
	} else if strings.HasPrefix(sqlUpper, "DROP TABLE") {
		return e.generateMySQLCreateTableRollback(sql, dbName)
	} else if strings.HasPrefix(sqlUpper, "ALTER TABLE") {
		return e.generateMySQLAlterTableRollback(sql)
	} else if strings.HasPrefix(sqlUpper, "CREATE TABLE") {
		return e.generateMySQLDropTableRollback(sql)
	} else if strings.HasPrefix(sqlUpper, "TRUNCATE") {
		return e.generateMySQLTruncateRollback(sql, dbName)
	}

	return fmt.Sprintf("-- MySQL回滚SQL（需要手动确认）:\n-- 原始SQL: %s\n-- 请根据具体操作手动编写回滚语句", sql), nil
}

// generateMySQLDeleteRollback 生成DELETE回滚SQL（针对INSERT）
func (e *MySQLEngine) generateMySQLDeleteRollback(sql string) (string, error) {
	// 简化实现：提取表名和可能的条件
	// 实际生产环境中应该解析INSERT语句的具体内容
	return fmt.Sprintf("-- INSERT语句的MySQL回滚SQL（需要手动确认）:\n-- 请根据实际插入的数据生成对应的DELETE语句\n-- 原始SQL: %s\n-- 示例: DELETE FROM table_name WHERE condition;", sql), nil
}

// generateMySQLUpdateRollback 生成UPDATE回滚SQL
func (e *MySQLEngine) generateMySQLUpdateRollback(sql, dbName string) (string, error) {
	// 简化实现：提示需要查询更新前的数据
	return fmt.Sprintf("-- UPDATE语句的MySQL回滚SQL（需要手动确认）:\n-- 请先查询更新前的数据，然后生成对应的UPDATE语句\n-- 原始SQL: %s\n-- 建议: 在执行UPDATE前先备份相关数据", sql), nil
}

// generateMySQLInsertRollback 生成INSERT回滚SQL（针对DELETE）
func (e *MySQLEngine) generateMySQLInsertRollback(sql, dbName string) (string, error) {
	// 简化实现：提示需要备份删除的数据
	return fmt.Sprintf("-- DELETE语句的MySQL回滚SQL（需要手动确认）:\n-- 请根据删除的数据生成对应的INSERT语句\n-- 原始SQL: %s\n-- 建议: 在执行DELETE前先备份相关数据", sql), nil
}

// generateMySQLCreateTableRollback 生成CREATE TABLE回滚SQL（针对DROP TABLE）
func (e *MySQLEngine) generateMySQLCreateTableRollback(sql, dbName string) (string, error) {
	// 简化实现：提示需要重建表结构
	return fmt.Sprintf("-- DROP TABLE语句的MySQL回滚SQL（需要手动确认）:\n-- 请根据表结构生成对应的CREATE TABLE语句\n-- 原始SQL: %s\n-- 建议: 使用SHOW CREATE TABLE备份表结构", sql), nil
}

// generateMySQLAlterTableRollback 生成ALTER TABLE回滚SQL
func (e *MySQLEngine) generateMySQLAlterTableRollback(sql string) (string, error) {
	// 简化实现：提示需要反向操作
	return fmt.Sprintf("-- ALTER TABLE语句的MySQL回滚SQL（需要手动确认）:\n-- 请根据具体的ALTER操作生成反向操作\n-- 原始SQL: %s\n-- 例如: ADD COLUMN -> DROP COLUMN, MODIFY -> 恢复原类型", sql), nil
}

// generateMySQLDropTableRollback 生成DROP TABLE回滚SQL（针对CREATE TABLE）
func (e *MySQLEngine) generateMySQLDropTableRollback(sql string) (string, error) {
	// 简化实现：生成DROP TABLE语句
	return fmt.Sprintf("-- CREATE TABLE语句的MySQL回滚SQL:\n-- 原始SQL: %s\n-- 回滚SQL: DROP TABLE IF EXISTS table_name;", sql), nil
}

// generateMySQLTruncateRollback 生成TRUNCATE回滚SQL
func (e *MySQLEngine) generateMySQLTruncateRollback(sql, dbName string) (string, error) {
	// TRUNCATE操作无法回滚，只能提示
	return fmt.Sprintf("-- TRUNCATE语句无法自动回滚:\n-- 原始SQL: %s\n-- 警告: TRUNCATE操作会删除所有数据且无法恢复\n-- 建议: 使用DELETE FROM table_name代替TRUNCATE", sql), nil
}

// KillConnection 终止连接
func (e *MySQLEngine) KillConnection(threadID int) error {
	db, err := e.GetConnection("")
	if err != nil {
		return err
	}

	sql := fmt.Sprintf("KILL %d", threadID)
	_, err = db.Exec(sql)
	return err
}
