import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [
        ElementPlusResolver(),
        IconsResolver({
          prefix: 'Icon',
        }),
      ],
      imports: [
        'vue',
        'vue-router',
        'pinia',
        '@vueuse/core'
      ],
      dts: true,
    }),
    Components({
      resolvers: [
        ElementPlusResolver(),
        IconsResolver({
          enabledCollections: ['ep'],
        }),
      ],
      dts: true,
    }),
    Icons({
      autoInstall: true,
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/api': resolve(__dirname, 'src/api'),
      '@/assets': resolve(__dirname, 'src/assets'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/layouts': resolve(__dirname, 'src/layouts'),
      '@/router': resolve(__dirname, 'src/router'),
      '@/stores': resolve(__dirname, 'src/stores'),
      '@/styles': resolve(__dirname, 'src/styles'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/views': resolve(__dirname, 'src/views'),
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "@/styles/variables.scss" as *;`,
      },
    },
  },
  server: {
    port: 5174,
    host: true,
    proxy: {
      // CI/CD服务代理
      '/api/v1/cicd': {
        target: 'http://localhost:9980',
        secure: false,
        changeOrigin: true,
        // pathRewrite: {
        //   '^/api/v1/cicd': '/api/v1'
        // }
      },
      '/api/v1/cicd/pipeline/tasks': {
        target: 'http://localhost:9980',
        secure: false,
        changeOrigin: true,
      },
      '/api/v1/sql': {
        target: 'http://localhost:9986',
        secure: false,
        changeOrigin: true
      },
      '/api/v1/cmdb': {
        target: 'http://localhost:9981',
        secure: false,
        changeOrigin: true
      },
      '/api/v1/ucenter': {
        target: 'http://localhost:9982',
        secure: false,
        changeOrigin: true
      },
      '/api/v1/system': {
        target: 'http://localhost:9982',
        secure: false,
        changeOrigin: true
      },
      '/api/v1/workflow': {
        target: 'http://localhost:9983',
        secure: false,
        changeOrigin: true
      },
      '/api/v1/kubernetes': {
        target: 'http://localhost:9984',
        secure: false,
        changeOrigin: true
      },
      // SQL服务代理
      '/api/sql': {
        target: 'http://localhost:9983',
        secure: false,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/sql/, '/api')
      },
      // 用户相关API代理
      // '/api/v1/user': {
      //   target: 'http://localhost:8091',
      //   secure: false,
      //   changeOrigin: true,
      //   rewrite: (path) => path.replace(/^\/api\/v1\/user/, ''),
      //   timeout: 30000,
      // },
      // // 登录相关API代理
      // '/api/login': {
      //   target: 'http://localhost:8091',
      //   secure: false,
      //   changeOrigin: true,
      //   timeout: 30000,
      // },
      // 其他API代理
      '/api': {
        target: 'http://localhost:8091',
        secure: false,
        changeOrigin: true,
        timeout: 30000,
      }
    }
  },
  define: {
    __VUE_PROD_DEVTOOLS__: false,
  },
  build: {
    // 设置最大chunk大小警告限制
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        // 分包策略
        manualChunks: {
          'element-plus': ['element-plus'],
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          'utils': ['axios', 'dayjs', 'lodash-es'],
        }
      }
    }
  }
})
