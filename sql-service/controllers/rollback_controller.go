package controllers

import (
	"strconv"

	"github.com/devops-microservices/sql-service/models"
	"github.com/devops-microservices/sql-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// RollbackController SQL回滚控制器
type RollbackController struct {
	*BaseController
	service services.SqlService
}

// NewRollbackController 创建SQL回滚控制器
func NewRollbackController(service services.SqlService, log *logrus.Logger) *RollbackController {
	return &RollbackController{
		BaseController: NewBaseController(log),
		service:        service,
	}
}

// GetRollbacks 获取回滚记录列表
// @Summary 获取回滚记录列表
// @Description 获取SQL回滚记录列表，支持分页和按工单ID筛选
// @Tags rollback
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param workflow_id query int false "工单ID"
// @Success 200 {object} models.APIResponse{data=models.PaginatedResponse}
// @Failure 400 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/rollbacks [get]
func (rc *RollbackController) GetRollbacks(c *gin.Context) {
	rc.LogRequest(c, "获取回滚记录列表")

	// 解析分页参数
	page, pageSize := rc.GetPaginationParams(c)

	// 解析工单ID参数
	workflowIDStr := c.Query("workflow_id")
	var workflowID uint
	if workflowIDStr != "" {
		if id, err := strconv.ParseUint(workflowIDStr, 10, 32); err == nil {
			workflowID = uint(id)
		}
	}

	// 获取回滚记录列表
	rollbacks, total, err := rc.service.GetSqlRollbacks(page, pageSize, workflowID)
	if err != nil {
		rc.log.Errorf("获取回滚记录列表失败: %v", err)
		rc.InternalErrorResponse(c, "获取回滚记录列表失败")
		return
	}

	// 构建响应数据
	response := &models.PaginatedResponse{
		Items:      rollbacks,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
	}

	rc.SuccessResponse(c, response)
}

// GetRollback 获取回滚记录详情
// @Summary 获取回滚记录详情
// @Description 根据ID获取SQL回滚记录详情
// @Tags rollback
// @Accept json
// @Produce json
// @Param id path int true "回滚记录ID"
// @Success 200 {object} models.APIResponse{data=models.SqlRollback}
// @Failure 400 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/rollbacks/{id} [get]
func (rc *RollbackController) GetRollback(c *gin.Context) {
	rc.LogRequest(c, "获取回滚记录详情")

	// 获取ID参数
	id, err := rc.GetIDParam(c)
	if err != nil {
		rc.BadRequestResponse(c, "无效的回滚记录ID")
		return
	}

	if id == 0 {
		return
	}

	// 获取回滚记录
	rollback, err := rc.service.GetSqlRollbackByID(id)
	if err != nil {
		rc.log.Errorf("获取回滚记录失败: %v", err)
		rc.NotFoundResponse(c, "回滚记录不存在")
		return
	}

	rc.SuccessResponse(c, rollback)
}

// CreateRollback 创建回滚记录
// @Summary 创建回滚记录
// @Description 为指定工单创建回滚记录
// @Tags rollback
// @Accept json
// @Produce json
// @Param request body models.SqlRollbackRequest true "回滚请求"
// @Success 200 {object} models.APIResponse{data=models.SqlRollback}
// @Failure 400 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/rollbacks [post]
func (rc *RollbackController) CreateRollback(c *gin.Context) {
	rc.LogRequest(c, "创建回滚记录")

	// 解析请求参数
	var req models.SqlRollbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		rc.BadRequestResponse(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取当前用户
	username := rc.GetCurrentUser(c)

	// 创建回滚记录
	rollback, err := rc.service.CreateSqlRollback(req.WorkflowID, username)
	if err != nil {
		rc.log.Errorf("创建回滚记录失败: %v", err)
		rc.ErrorResponse(c, 400, err.Error())
		return
	}

	rc.SuccessResponse(c, rollback)
}

// ExecuteRollback 执行回滚
// @Summary 执行回滚
// @Description 执行指定的SQL回滚记录
// @Tags rollback
// @Accept json
// @Produce json
// @Param id path int true "回滚记录ID"
// @Success 200 {object} models.APIResponse
// @Failure 400 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/rollbacks/{id}/execute [post]
func (rc *RollbackController) ExecuteRollback(c *gin.Context) {
	rc.LogRequest(c, "执行回滚")

	// 获取ID参数
	id, err := rc.GetIDParam(c)
	if err != nil {
		rc.BadRequestResponse(c, "无效的回滚记录ID")
		return
	}

	if id == 0 {
		return
	}

	// 获取当前用户
	username := rc.GetCurrentUser(c)

	// 检查用户权限
	isAdmin := rc.IsAdmin(c)
	if !isAdmin {
		rc.ErrorResponse(c, 403, "只有管理员才能执行回滚操作")
		return
	}

	// 执行回滚
	if err := rc.service.ExecuteSqlRollback(id, username); err != nil {
		rc.log.Errorf("执行回滚失败: %v", err)
		rc.ErrorResponse(c, 400, err.Error())
		return
	}

	rc.SuccessResponse(c, map[string]interface{}{
		"message": "回滚执行成功",
	})
}

// GetWorkflowRollbacks 获取工单的回滚记录
// @Summary 获取工单的回滚记录
// @Description 获取指定工单的所有回滚记录
// @Tags rollback
// @Accept json
// @Produce json
// @Param workflow_id path int true "工单ID"
// @Success 200 {object} models.APIResponse{data=[]models.SqlRollback}
// @Failure 400 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/workflows/{workflow_id}/rollbacks [get]
func (rc *RollbackController) GetWorkflowRollbacks(c *gin.Context) {
	rc.LogRequest(c, "获取工单回滚记录")

	// 获取工单ID参数
	workflowIDStr := c.Param("workflow_id")
	workflowID, err := strconv.ParseUint(workflowIDStr, 10, 32)
	if err != nil {
		rc.BadRequestResponse(c, "工单ID格式错误")
		return
	}

	// 获取回滚记录（不分页，获取所有记录）
	rollbacks, _, err := rc.service.GetSqlRollbacks(1, 1000, uint(workflowID))
	if err != nil {
		rc.log.Errorf("获取工单回滚记录失败: %v", err)
		rc.InternalErrorResponse(c, "获取工单回滚记录失败")
		return
	}

	rc.SuccessResponse(c, rollbacks)
}

// CanRollback 检查工单是否可以回滚
// @Summary 检查工单是否可以回滚
// @Description 检查指定工单是否可以创建回滚记录
// @Tags rollback
// @Accept json
// @Produce json
// @Param workflow_id path int true "工单ID"
// @Success 200 {object} models.APIResponse{data=map[string]bool}
// @Failure 400 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/workflows/{workflow_id}/can-rollback [get]
func (rc *RollbackController) CanRollback(c *gin.Context) {
	rc.LogRequest(c, "检查工单是否可以回滚")

	// 获取工单ID参数
	workflowIDStr := c.Param("workflow_id")
	workflowID, err := strconv.ParseUint(workflowIDStr, 10, 32)
	if err != nil {
		rc.BadRequestResponse(c, "工单ID格式错误")
		return
	}

	// 获取工单信息
	workflow, err := rc.service.GetSqlWorkflowByID(uint(workflowID))
	if err != nil {
		rc.NotFoundResponse(c, "工单不存在")
		return
	}

	// 检查工单状态
	canRollback := workflow.Status == "workflow_finish"

	// 检查是否已有回滚记录
	if canRollback {
		rollbacks, _, err := rc.service.GetSqlRollbacks(1, 1, uint(workflowID))
		if err == nil && len(rollbacks) > 0 {
			canRollback = false
		}
	}

	rc.SuccessResponse(c, map[string]interface{}{
		"can_rollback": canRollback,
		"reason":       rc.getRollbackReason(workflow, canRollback),
	})
}

// getRollbackReason 获取不能回滚的原因
func (rc *RollbackController) getRollbackReason(workflow *models.SqlWorkflow, canRollback bool) string {
	if canRollback {
		return "可以回滚"
	}

	if workflow.Status != "workflow_finish" {
		return "只有已执行完成的工单才能回滚"
	}

	return "该工单已存在回滚记录"
}
