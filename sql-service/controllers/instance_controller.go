package controllers

import (
	"github.com/devops-microservices/sql-service/models"
	"github.com/devops-microservices/sql-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// InstanceController 数据库实例控制器
type InstanceController struct {
	*BaseController
	service services.SqlService
}

// NewInstanceController 创建数据库实例控制器
func NewInstanceController(service services.SqlService, log *logrus.Logger) *InstanceController {
	return &InstanceController{
		BaseController: NewBaseController(log),
		service:        service,
	}
}

// GetInstances 获取数据库实例列表
// @Summary 获取数据库实例列表
// @Description 获取数据库实例列表，支持分页和搜索
// @Tags 数据库实例
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Success 200 {object} models.APIResponse{data=models.PaginatedResponse}
// @Router /instance [get]
func (ic *InstanceController) GetInstances(c *gin.Context) {
	ic.LogRequest(c, "获取数据库实例列表")

	page, pageSize := ic.GetPaginationParams(c)
	search := ic.GetSearchParam(c)

	instances, total, err := ic.service.GetDBInstances(page, pageSize, search)
	if err != nil {
		ic.log.Errorf("获取数据库实例列表失败: %v", err)
		ic.InternalErrorResponse(c, "获取数据库实例列表失败")
		return
	}

	ic.PaginatedResponse(c, instances, total, page, pageSize)
}

// GetInstance 获取数据库实例详情
// @Summary 获取数据库实例详情
// @Description 根据ID获取数据库实例详情
// @Tags 数据库实例
// @Accept json
// @Produce json
// @Param id path int true "实例ID"
// @Success 200 {object} models.APIResponse{data=models.DBInstance}
// @Router /instance/{id} [get]
func (ic *InstanceController) GetInstance(c *gin.Context) {
	ic.LogRequest(c, "获取数据库实例详情")

	id, err := ic.GetIDParam(c)
	if err != nil {
		ic.BadRequestResponse(c, "无效的实例ID")
		return
	}

	instance, err := ic.service.GetDBInstanceByID(id)
	if err != nil {
		ic.log.Errorf("获取数据库实例详情失败: %v", err)
		ic.NotFoundResponse(c, "数据库实例不存在")
		return
	}

	ic.SuccessResponse(c, instance)
}

// CreateInstance 创建数据库实例
// @Summary 创建数据库实例
// @Description 创建新的数据库实例
// @Tags 数据库实例
// @Accept json
// @Produce json
// @Param instance body models.DBInstanceCreateRequest true "实例信息"
// @Success 200 {object} models.APIResponse{data=models.DBInstance}
// @Router /instance [post]
func (ic *InstanceController) CreateInstance(c *gin.Context) {
	ic.LogRequest(c, "创建数据库实例")

	var req models.DBInstanceCreateRequest
	if !ic.ValidateJSON(c, &req) {
		return
	}

	instance, err := ic.service.CreateDBInstance(&req)
	if err != nil {
		ic.log.Errorf("创建数据库实例失败: %v", err)
		ic.InternalErrorResponse(c, "创建数据库实例失败: "+err.Error())
		return
	}

	ic.SuccessResponse(c, instance)
}

// UpdateInstance 更新数据库实例
// @Summary 更新数据库实例
// @Description 更新数据库实例信息
// @Tags 数据库实例
// @Accept json
// @Produce json
// @Param id path int true "实例ID"
// @Param instance body models.DBInstanceCreateRequest true "实例信息"
// @Success 200 {object} models.APIResponse{data=models.DBInstance}
// @Router /instance/{id} [put]
func (ic *InstanceController) UpdateInstance(c *gin.Context) {
	ic.LogRequest(c, "更新数据库实例")

	id, err := ic.GetIDParam(c)
	if err != nil {
		ic.BadRequestResponse(c, "无效的实例ID")
		return
	}

	var req models.DBInstanceCreateRequest
	if !ic.ValidateJSON(c, &req) {
		return
	}

	updates := map[string]interface{}{
		"alias":        req.Alias,
		"type":         req.Type,
		"db_type":      req.DBType,
		"mode":         req.Mode,
		"host":         req.Host,
		"port":         req.Port,
		"user":         req.User,
		"password":     req.Password,
		"db_name":      req.DBName,
		"charset":      req.Charset,
		"auto_backup":  req.AutoBackup,
		"projects":     req.Projects,
		"environments": req.Environments,
	}

	instance, err := ic.service.UpdateDBInstance(id, updates)
	if err != nil {
		ic.log.Errorf("更新数据库实例失败: %v", err)
		ic.InternalErrorResponse(c, "更新数据库实例失败: "+err.Error())
		return
	}

	ic.SuccessResponse(c, instance)
}

// DeleteInstance 删除数据库实例
// @Summary 删除数据库实例
// @Description 删除数据库实例（软删除）
// @Tags 数据库实例
// @Accept json
// @Produce json
// @Param id path int true "实例ID"
// @Success 200 {object} models.APIResponse
// @Router /instance/{id} [delete]
func (ic *InstanceController) DeleteInstance(c *gin.Context) {
	ic.LogRequest(c, "删除数据库实例")

	id, err := ic.GetIDParam(c)
	if err != nil {
		ic.BadRequestResponse(c, "无效的实例ID")
		return
	}

	if err := ic.service.DeleteDBInstance(id); err != nil {
		ic.log.Errorf("删除数据库实例失败: %v", err)
		ic.InternalErrorResponse(c, "删除数据库实例失败: "+err.Error())
		return
	}

	ic.SuccessResponse(c, "删除成功")
}

// GetUserInstances 获取用户有权限的数据库实例
// @Summary 获取用户有权限的数据库实例
// @Description 获取当前用户有权限访问的数据库实例列表
// @Tags 数据库实例
// @Accept json
// @Produce json
// @Success 200 {object} models.APIResponse{data=[]models.DBInstance}
// @Router /instance/user/instances [get]
func (ic *InstanceController) GetUserInstances(c *gin.Context) {
	ic.LogRequest(c, "获取用户数据库实例")

	username := ic.GetCurrentUser(c)
	isAdmin := ic.IsAdmin(c)

	instances, err := ic.service.GetUserInstances(username, isAdmin)
	if err != nil {
		ic.log.Errorf("获取用户数据库实例失败: %v", err)
		ic.InternalErrorResponse(c, "获取用户数据库实例失败")
		return
	}

	ic.SuccessResponse(c, map[string]interface{}{
		"items": instances,
	})
}

// TestConnection 测试数据库连接
// @Summary 测试数据库连接
// @Description 测试数据库实例的连接状态
// @Tags 数据库实例
// @Accept json
// @Produce json
// @Param id path int true "实例ID"
// @Success 200 {object} models.APIResponse
// @Router /instance/{id}/test [post]
func (ic *InstanceController) TestConnection(c *gin.Context) {
	ic.LogRequest(c, "测试数据库连接")

	id, err := ic.GetIDParam(c)
	if err != nil {
		ic.BadRequestResponse(c, "无效的实例ID")
		return
	}

	instance, err := ic.service.GetDBInstanceByID(id)
	if err != nil {
		ic.NotFoundResponse(c, "数据库实例不存在")
		return
	}

	// TODO: 实现实际的连接测试逻辑
	// 这里应该使用SQL引擎来测试连接

	ic.SuccessResponse(c, map[string]interface{}{
		"status":  "success",
		"message": "连接测试成功",
		"instance": map[string]interface{}{
			"id":            instance.ID,
			"instance_name": instance.InstanceName,
			"host":          instance.Host,
			"port":          instance.Port,
			"db_type":       instance.DBType,
		},
	})
}

// GetDatabases 获取实例下的数据库列表
// @Summary 获取实例下的数据库列表
// @Description 获取指定实例下的所有数据库
// @Tags 数据库实例
// @Accept json
// @Produce json
// @Param id path int true "实例ID"
// @Success 200 {object} models.APIResponse{data=[]string}
// @Router /instance/{id}/databases [get]
func (ic *InstanceController) GetDatabases(c *gin.Context) {
	ic.LogRequest(c, "获取数据库列表")

	id, err := ic.GetIDParam(c)
	if err != nil {
		ic.BadRequestResponse(c, "无效的实例ID")
		return
	}

	instance, err := ic.service.GetDBInstanceByID(id)
	if err != nil {
		ic.NotFoundResponse(c, "数据库实例不存在")
		return
	}

	// TODO: 实现获取数据库列表的逻辑
	// 这里应该使用SQL引擎来获取数据库列表

	databases := []string{"test_db", "prod_db"} // 示例数据

	ic.SuccessResponse(c, map[string]interface{}{
		"items": databases,
		"instance": map[string]interface{}{
			"id":            instance.ID,
			"instance_name": instance.InstanceName,
			"db_type":       instance.DBType,
		},
	})
}
