package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/devops-microservices/sql-service/config"
	_ "github.com/devops-microservices/sql-service/docs"
	"github.com/devops-microservices/sql-service/routes"
	"github.com/devops-microservices/sql-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

var (
	// 启动优化标志
	skipMigration    = flag.Bool("skip-migration", false, "跳过数据库迁移检查（加快启动）")
	forceAutoMigrate = flag.Bool("force-migrate", false, "强制执行数据库迁移，即使设置了skip-migration")
)

// initLogger 初始化日志
func initLogger() *logrus.Logger {
	log := logrus.New()
	log.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
		ForceColors:   true,
	})
	log.SetLevel(logrus.InfoLevel)
	return log
}

// initGin 初始化Gin引擎
func initGin(log *logrus.Logger) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()

	// 添加中间件
	router.Use(gin.Recovery())
	router.Use(func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)
		log.WithFields(logrus.Fields{
			"method":   c.Request.Method,
			"path":     c.Request.URL.Path,
			"status":   c.Writer.Status(),
			"duration": duration,
			"ip":       c.ClientIP(),
		}).Info("HTTP Request")
	})

	return router
}

// @title SQL Service API
// @version 1.0
// @description DevOps微服务平台的SQL管理服务
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:9983
// @BasePath /api/sql
func main() {
	// 解析命令行参数
	var port int
	var configPath string
	flag.IntVar(&port, "port", 0, "服务端口，默认使用配置文件中的端口")
	flag.StringVar(&configPath, "config", "", "配置文件路径，默认使用工作目录下的config.yaml")
	flag.Parse()

	startTime := time.Now()

	// 初始化日志
	log := initLogger()
	log.Info("🚀 初始化SQL服务")

	// 加载配置
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 验证配置
	if err := config.ValidateConfig(cfg); err != nil {
		log.Fatalf("❌ 配置验证失败: %v", err)
	}

	// 如果命令行指定了端口，则覆盖配置文件中的端口
	if port > 0 {
		log.Infof("🔧 使用命令行指定的端口: %d", port)
		cfg.Server.Port = port
	}

	// 初始化数据库
	log.Info("📊 初始化数据库...")
	db, err := config.InitDatabase(&cfg.Database, log)
	if err != nil {
		log.Fatalf("❌ 初始化数据库失败: %v", err)
	}

	// 数据库迁移
	if !*skipMigration || *forceAutoMigrate {
		log.Info("🔄 执行数据库迁移...")
		if err := config.AutoMigrate(db, log); err != nil {
			log.Fatalf("❌ 数据库迁移失败: %v", err)
		}
		log.Info("✅ 数据库迁移完成")
	} else {
		log.Info("⏭️  跳过数据库迁移")
	}

	// 初始化Gin
	router := initGin(log)

	// 初始化调度器服务
	log.Info("🕐 初始化任务调度器...")
	sqlService := services.NewSqlService(db, log, cfg)
	schedulerService := services.NewSchedulerService(db, sqlService, cfg, log)

	// 如果配置启用调度器，则启动
	if cfg.Scheduler.Enabled {
		if err := schedulerService.Start(); err != nil {
			log.Fatalf("❌ 启动任务调度器失败: %v", err)
		}

		// 添加默认的同步用户任务
		if err := schedulerService.AddSyncUserSchedule(); err != nil {
			log.Warnf("⚠️ 添加同步用户任务失败: %v", err)
		}
	} else {
		log.Info("⏭️  任务调度器已禁用")
	}

	// 注册路由
	routes.SetupRoutes(router, db, cfg, log, schedulerService)

	// 启动HTTP服务器
	srv := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}

	// 优雅启动和关闭
	go func() {
		log.Infof("🌐 SQL服务启动在 %s:%d", cfg.Server.Host, cfg.Server.Port)
		log.Infof("📖 API文档地址: http://%s:%d/swagger/index.html", cfg.Server.Host, cfg.Server.Port)
		log.Infof("⚡ 启动耗时: %v", time.Since(startTime))

		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("❌ 启动服务器失败: %v", err)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Info("🛑 正在关闭SQL服务...")

	// 停止调度器
	if cfg.Scheduler.Enabled {
		log.Info("🛑 正在停止任务调度器...")
		if err := schedulerService.Stop(); err != nil {
			log.Errorf("❌ 停止任务调度器失败: %v", err)
		} else {
			log.Info("✅ 任务调度器已停止")
		}
	}

	// 给服务器5秒钟来完成现有请求
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("❌ 服务器强制关闭: %v", err)
	}

	log.Info("✅ SQL服务已关闭")
}
