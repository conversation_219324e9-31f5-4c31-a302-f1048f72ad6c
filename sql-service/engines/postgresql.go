package engines

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/devops-microservices/sql-service/models"
	_ "github.com/lib/pq"
)

// PostgreSQLEngine PostgreSQL数据库引擎
type PostgreSQLEngine struct {
	*BaseEngine
}

// NewPostgreSQLEngine 创建PostgreSQL引擎
func NewPostgreSQLEngine(instance *models.DBInstance) *PostgreSQLEngine {
	return &PostgreSQLEngine{
		BaseEngine: NewBaseEngine(instance),
	}
}

// GetConnection 获取数据库连接
func (e *PostgreSQLEngine) GetConnection(dbName string) (*sql.DB, error) {
	if e.Connection != nil {
		return e.Connection, nil
	}

	if dbName == "" {
		dbName = e.Instance.DBName
	}

	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
		e.Instance.Host, e.Instance.Port, e.Instance.User, e.Instance.Password, dbName)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, err
	}

	if err := db.Ping(); err != nil {
		return nil, err
	}

	e.Connection = db
	return db, nil
}

// TestConnection 测试连接
func (e *PostgreSQLEngine) TestConnection() error {
	db, err := e.GetConnection(e.Instance.DBName)
	if err != nil {
		return err
	}
	return db.Ping()
}

// Close 关闭连接
func (e *PostgreSQLEngine) Close() error {
	if e.Connection != nil {
		return e.Connection.Close()
	}
	return nil
}

// Query 执行查询
func (e *PostgreSQLEngine) Query(dbName, sql string, limitNum int, schemaName, tbName string, maxExecutionTime int) (*QueryResult, error) {
	db, err := e.GetConnection(dbName)
	if err != nil {
		return nil, err
	}

	// 添加LIMIT限制
	if limitNum > 0 && !strings.Contains(strings.ToUpper(sql), "LIMIT") {
		sql = BuildLimitSQL(sql, limitNum, "postgres")
	}

	start := time.Now()
	rows, err := db.Query(sql)
	if err != nil {
		return &QueryResult{
			Error:     FormatError(err),
			QueryTime: time.Since(start),
		}, nil
	}
	defer rows.Close()

	data, columns, err := ConvertRowsToInterface(rows)
	if err != nil {
		return &QueryResult{
			Error:     FormatError(err),
			QueryTime: time.Since(start),
		}, nil
	}

	return &QueryResult{
		Columns:             columns,
		Rows:                data,
		AffectedRows:        int64(len(data)),
		QueryTime:           time.Since(start),
		SecondsBehindMaster: 0, // PostgreSQL没有主从延迟概念
	}, nil
}

// QueryCheck 查询检查
func (e *PostgreSQLEngine) QueryCheck(dbName, sql string) map[string]interface{} {
	result := map[string]interface{}{
		"bad_query":    false,
		"has_star":     false,
		"filtered_sql": sql,
		"msg":          "",
	}

	// 检查是否包含SELECT *
	if strings.Contains(strings.ToUpper(sql), "SELECT *") {
		result["has_star"] = true
		result["msg"] = "查询语句包含SELECT *，可能影响性能"
	}

	// 检查危险操作
	dangerousKeywords := []string{"DROP", "DELETE", "TRUNCATE", "ALTER"}
	upperSQL := strings.ToUpper(sql)
	for _, keyword := range dangerousKeywords {
		if strings.Contains(upperSQL, keyword) {
			result["bad_query"] = true
			result["msg"] = fmt.Sprintf("查询语句包含危险操作: %s", keyword)
			break
		}
	}

	return result
}

// FilterSQL 过滤SQL语句
func (e *PostgreSQLEngine) FilterSQL(sql string, limitNum int) string {
	return BuildLimitSQL(sql, limitNum, "postgres")
}

// GetAllDatabases 获取所有数据库
func (e *PostgreSQLEngine) GetAllDatabases() (*QueryResult, error) {
	sql := "SELECT datname FROM pg_database WHERE datistemplate = false"
	return e.Query("postgres", sql, 0, "", "", 0)
}

// GetAllSchemas 获取所有模式
func (e *PostgreSQLEngine) GetAllSchemas(dbName string) (*QueryResult, error) {
	sql := "SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')"
	return e.Query(dbName, sql, 0, "", "", 0)
}

// GetAllTables 获取所有表
func (e *PostgreSQLEngine) GetAllTables(dbName, schemaName string) (*QueryResult, error) {
	if schemaName == "" {
		schemaName = "public"
	}
	sql := fmt.Sprintf("SELECT table_name FROM information_schema.tables WHERE table_schema='%s' AND table_type='BASE TABLE'", schemaName)
	return e.Query(dbName, sql, 0, "", "", 0)
}

// GetAllColumnsByTable 获取表的所有列
func (e *PostgreSQLEngine) GetAllColumnsByTable(dbName, tbName, schemaName string) (*QueryResult, error) {
	if schemaName == "" {
		schemaName = "public"
	}
	sql := fmt.Sprintf("SELECT column_name FROM information_schema.columns WHERE table_schema='%s' AND table_name='%s' ORDER BY ordinal_position", schemaName, tbName)
	return e.Query(dbName, sql, 0, "", "", 0)
}

// GetTableMetaData 获取表元数据
func (e *PostgreSQLEngine) GetTableMetaData(dbName, tbName string) map[string]interface{} {
	sql := fmt.Sprintf("SELECT schemaname, tablename, tableowner FROM pg_tables WHERE tablename='%s'", tbName)
	result, err := e.Query(dbName, sql, 0, "", "", 0)
	if err != nil || len(result.Rows) == 0 {
		return map[string]interface{}{"rows": []interface{}{tbName, "", "", 0}}
	}
	return map[string]interface{}{"rows": result.Rows[0]}
}

// GetTableDescData 获取表结构数据
func (e *PostgreSQLEngine) GetTableDescData(dbName, tbName, schemaName string) map[string]interface{} {
	if schemaName == "" {
		schemaName = "public"
	}
	sql := fmt.Sprintf(`
		SELECT column_name, data_type, is_nullable, column_default, character_maximum_length
		FROM information_schema.columns 
		WHERE table_schema='%s' AND table_name='%s' 
		ORDER BY ordinal_position`, schemaName, tbName)

	result, err := e.Query(dbName, sql, 0, "", "", 0)
	if err != nil {
		return map[string]interface{}{"error": err.Error()}
	}
	return map[string]interface{}{
		"column_list": result.Columns,
		"rows":        result.Rows,
	}
}

// GetTableIndexData 获取表索引数据
func (e *PostgreSQLEngine) GetTableIndexData(dbName, tbName string) map[string]interface{} {
	sql := fmt.Sprintf(`
		SELECT indexname, indexdef 
		FROM pg_indexes 
		WHERE tablename='%s'`, tbName)

	result, err := e.Query(dbName, sql, 0, "", "", 0)
	if err != nil {
		return map[string]interface{}{"error": err.Error()}
	}
	return map[string]interface{}{
		"column_list": result.Columns,
		"rows":        result.Rows,
	}
}

// GetTableCreate 获取建表语句
func (e *PostgreSQLEngine) GetTableCreate(dbName, tbName, schemaName string) (*QueryResult, error) {
	// PostgreSQL没有直接的SHOW CREATE TABLE，需要构造
	if schemaName == "" {
		schemaName = "public"
	}

	// 这里简化处理，实际应该构造完整的CREATE TABLE语句
	sql := fmt.Sprintf(`
		SELECT 'CREATE TABLE %s.%s (' || string_agg(
			column_name || ' ' || data_type || 
			CASE WHEN character_maximum_length IS NOT NULL 
				THEN '(' || character_maximum_length || ')' 
				ELSE '' 
			END ||
			CASE WHEN is_nullable = 'NO' THEN ' NOT NULL' ELSE '' END,
			', '
		) || ');' as create_sql
		FROM information_schema.columns 
		WHERE table_schema='%s' AND table_name='%s'
		GROUP BY table_schema, table_name`, schemaName, tbName, schemaName, tbName)

	return e.Query(dbName, sql, 0, "", "", 0)
}

// DescribeTable 描述表结构
func (e *PostgreSQLEngine) DescribeTable(dbName, tbName, schemaName string) (*QueryResult, error) {
	descData := e.GetTableDescData(dbName, tbName, schemaName)

	// 转换为QueryResult格式
	if columnList, ok := descData["column_list"].([]string); ok {
		if rows, ok := descData["rows"].([][]interface{}); ok {
			return &QueryResult{
				Columns:      columnList,
				Rows:         rows,
				AffectedRows: int64(len(rows)),
				QueryTime:    0,
			}, nil
		}
	}

	// 如果转换失败，返回空结果
	return &QueryResult{
		Columns:      []string{},
		Rows:         [][]interface{}{},
		AffectedRows: 0,
		QueryTime:    0,
	}, nil
}

// ExecuteCheck 执行检查
func (e *PostgreSQLEngine) ExecuteCheck(dbName, sql string) (*ReviewSet, error) {
	// 基础SQL语法检查
	if err := ValidateSQL(sql); err != nil {
		return &ReviewSet{
			FullSQL:      sql,
			IsExecute:    false,
			Checked:      &[]bool{true}[0],
			WarningCount: 0,
			ErrorCount:   1,
			IsCritical:   true,
			SyntaxType:   ParseSQLType(sql),
			Error:        &[]string{err.Error()}[0],
			Rows: []ReviewResult{
				{
					ID:           1,
					Stage:        "CHECKED",
					ErrLevel:     2,
					StageStatus:  "Audit failed",
					ErrorMessage: err.Error(),
					SQL:          sql,
					AffectedRows: 0,
					Sequence:     "1",
				},
			},
			AffectedRows: 0,
		}, nil
	}

	// 执行EXPLAIN检查SQL性能
	db, err := e.GetConnection(dbName)
	if err != nil {
		return nil, err
	}

	explainSQL := "EXPLAIN " + sql
	rows, err := db.Query(explainSQL)
	if err != nil {
		// SQL语法错误
		return &ReviewSet{
			FullSQL:      sql,
			IsExecute:    false,
			Checked:      &[]bool{true}[0],
			WarningCount: 0,
			ErrorCount:   1,
			IsCritical:   true,
			SyntaxType:   ParseSQLType(sql),
			Error:        &[]string{err.Error()}[0],
			Rows: []ReviewResult{
				{
					ID:           1,
					Stage:        "CHECKED",
					ErrLevel:     2,
					StageStatus:  "Audit failed",
					ErrorMessage: err.Error(),
					SQL:          sql,
					AffectedRows: 0,
					Sequence:     "1",
				},
			},
			AffectedRows: 0,
		}, nil
	}
	defer rows.Close()

	// SQL检查通过
	reviewSet := &ReviewSet{
		FullSQL:      sql,
		IsExecute:    true,
		Checked:      &[]bool{true}[0],
		WarningCount: 0,
		ErrorCount:   0,
		IsCritical:   false,
		SyntaxType:   ParseSQLType(sql),
		Rows: []ReviewResult{
			{
				ID:           1,
				Stage:        "CHECKED",
				ErrLevel:     0,
				StageStatus:  "Audit completed",
				ErrorMessage: "",
				SQL:          sql,
				AffectedRows: 0,
				Sequence:     "1",
			},
		},
		AffectedRows: 0,
	}

	return reviewSet, nil
}

// Execute 执行SQL
func (e *PostgreSQLEngine) Execute(dbName, sql string) (*ReviewSet, error) {
	// 先检查SQL
	reviewSet, err := e.ExecuteCheck(dbName, sql)
	if err != nil {
		return nil, err
	}

	// 如果检查失败，不执行
	if reviewSet.ErrorCount > 0 {
		return reviewSet, nil
	}

	// 执行SQL
	db, err := e.GetConnection(dbName)
	if err != nil {
		return nil, err
	}

	start := time.Now()
	result, err := db.Exec(sql)
	executeTime := time.Since(start)

	if err != nil {
		// 执行失败
		reviewSet.ErrorCount = 1
		reviewSet.IsCritical = true
		reviewSet.Error = &[]string{err.Error()}[0]
		reviewSet.Rows[0].ErrLevel = 2
		reviewSet.Rows[0].StageStatus = "Execute failed"
		reviewSet.Rows[0].ErrorMessage = err.Error()
		reviewSet.Rows[0].ExecuteTime = executeTime.String()
		return reviewSet, nil
	}

	// 执行成功
	affectedRows, _ := result.RowsAffected()
	reviewSet.AffectedRows = affectedRows
	reviewSet.Rows[0].AffectedRows = affectedRows
	reviewSet.Rows[0].StageStatus = "Execute Successfully"
	reviewSet.Rows[0].ExecuteTime = executeTime.String()

	return reviewSet, nil
}

// GetRollback 获取回滚语句
func (e *PostgreSQLEngine) GetRollback(workflow *models.SqlWorkflow) ([][]string, error) {
	// 实现PostgreSQL的回滚语句生成逻辑
	rollbackSQL, err := e.generatePostgreSQLRollback(workflow.DBName, workflow.SqlContent)
	if err != nil {
		return nil, err
	}

	// 返回回滚语句列表
	return [][]string{{rollbackSQL}}, nil
}

// QueryMasking 查询脱敏
func (e *PostgreSQLEngine) QueryMasking(dbName, sql string, queryResult *QueryResult) (*QueryResult, error) {
	// 实现PostgreSQL的数据脱敏逻辑
	// PostgreSQL的数据脱敏可以通过修改查询结果来实现

	// 检查是否需要脱敏
	if !e.needsMasking(sql) {
		return queryResult, nil
	}

	// 应用脱敏规则
	maskedResult := &QueryResult{
		Columns:             queryResult.Columns,
		Rows:                e.maskPostgreSQLData(queryResult.Rows, queryResult.Columns),
		AffectedRows:        queryResult.AffectedRows,
		QueryTime:           queryResult.QueryTime,
		SecondsBehindMaster: queryResult.SecondsBehindMaster,
		IsMasked:            true,
		MaskRuleHit:         true,
	}

	return maskedResult, nil
}

// generatePostgreSQLRollback 生成PostgreSQL回滚SQL
func (e *PostgreSQLEngine) generatePostgreSQLRollback(dbName, sql string) (string, error) {
	sqlUpper := strings.ToUpper(strings.TrimSpace(sql))

	if strings.HasPrefix(sqlUpper, "INSERT") {
		return e.generatePostgreSQLDeleteRollback(sql)
	} else if strings.HasPrefix(sqlUpper, "UPDATE") {
		return e.generatePostgreSQLUpdateRollback(sql, dbName)
	} else if strings.HasPrefix(sqlUpper, "DELETE") {
		return e.generatePostgreSQLInsertRollback(sql, dbName)
	} else if strings.HasPrefix(sqlUpper, "DROP TABLE") {
		return e.generatePostgreSQLCreateTableRollback(sql, dbName)
	} else if strings.HasPrefix(sqlUpper, "ALTER TABLE") {
		return e.generatePostgreSQLAlterTableRollback(sql)
	} else if strings.HasPrefix(sqlUpper, "CREATE TABLE") {
		return e.generatePostgreSQLDropTableRollback(sql)
	}

	return fmt.Sprintf("-- PostgreSQL回滚SQL（需要手动确认）:\n-- 原始SQL: %s\n-- 请根据具体操作手动编写回滚语句", sql), nil
}

// generatePostgreSQLDeleteRollback 生成DELETE回滚SQL（针对INSERT）
func (e *PostgreSQLEngine) generatePostgreSQLDeleteRollback(sql string) (string, error) {
	return fmt.Sprintf("-- INSERT语句的PostgreSQL回滚SQL（需要手动确认）:\n-- 请根据实际插入的数据生成对应的DELETE语句\n-- 原始SQL: %s", sql), nil
}

// generatePostgreSQLUpdateRollback 生成UPDATE回滚SQL
func (e *PostgreSQLEngine) generatePostgreSQLUpdateRollback(sql, dbName string) (string, error) {
	return fmt.Sprintf("-- UPDATE语句的PostgreSQL回滚SQL（需要手动确认）:\n-- 请根据更新前的数据生成对应的UPDATE语句\n-- 原始SQL: %s", sql), nil
}

// generatePostgreSQLInsertRollback 生成INSERT回滚SQL（针对DELETE）
func (e *PostgreSQLEngine) generatePostgreSQLInsertRollback(sql, dbName string) (string, error) {
	return fmt.Sprintf("-- DELETE语句的PostgreSQL回滚SQL（需要手动确认）:\n-- 请根据删除的数据生成对应的INSERT语句\n-- 原始SQL: %s", sql), nil
}

// generatePostgreSQLCreateTableRollback 生成CREATE TABLE回滚SQL（针对DROP TABLE）
func (e *PostgreSQLEngine) generatePostgreSQLCreateTableRollback(sql, dbName string) (string, error) {
	return fmt.Sprintf("-- DROP TABLE语句的PostgreSQL回滚SQL（需要手动确认）:\n-- 请根据表结构生成对应的CREATE TABLE语句\n-- 原始SQL: %s", sql), nil
}

// generatePostgreSQLAlterTableRollback 生成ALTER TABLE回滚SQL
func (e *PostgreSQLEngine) generatePostgreSQLAlterTableRollback(sql string) (string, error) {
	return fmt.Sprintf("-- ALTER TABLE语句的PostgreSQL回滚SQL（需要手动确认）:\n-- 请根据具体的ALTER操作生成反向操作\n-- 原始SQL: %s", sql), nil
}

// generatePostgreSQLDropTableRollback 生成DROP TABLE回滚SQL（针对CREATE TABLE）
func (e *PostgreSQLEngine) generatePostgreSQLDropTableRollback(sql string) (string, error) {
	return fmt.Sprintf("-- CREATE TABLE语句的PostgreSQL回滚SQL（需要手动确认）:\n-- 请生成对应的DROP TABLE语句\n-- 原始SQL: %s", sql), nil
}

// needsMasking 检查是否需要脱敏
func (e *PostgreSQLEngine) needsMasking(sql string) bool {
	// 简化实现：检查是否为SELECT语句
	sqlUpper := strings.ToUpper(strings.TrimSpace(sql))
	return strings.HasPrefix(sqlUpper, "SELECT")
}

// maskPostgreSQLData 对PostgreSQL数据进行脱敏
func (e *PostgreSQLEngine) maskPostgreSQLData(rows [][]interface{}, columns []string) [][]interface{} {
	maskedRows := make([][]interface{}, len(rows))

	for i, row := range rows {
		maskedRow := make([]interface{}, len(row))
		for j, value := range row {
			// 根据列名判断是否需要脱敏
			if j < len(columns) {
				columnName := strings.ToLower(columns[j])
				maskedRow[j] = e.maskPostgreSQLValue(value, columnName)
			} else {
				maskedRow[j] = value
			}
		}
		maskedRows[i] = maskedRow
	}

	return maskedRows
}

// maskPostgreSQLValue 对单个值进行脱敏
func (e *PostgreSQLEngine) maskPostgreSQLValue(value interface{}, columnName string) interface{} {
	if value == nil {
		return nil
	}

	strValue := fmt.Sprintf("%v", value)
	if strValue == "" {
		return value
	}

	// 根据列名判断脱敏类型
	if strings.Contains(columnName, "phone") || strings.Contains(columnName, "mobile") {
		return e.maskPhone(strValue)
	} else if strings.Contains(columnName, "email") {
		return e.maskEmail(strValue)
	} else if strings.Contains(columnName, "name") && !strings.Contains(columnName, "username") {
		return e.maskName(strValue)
	} else if strings.Contains(columnName, "password") || strings.Contains(columnName, "pwd") {
		return "******"
	} else if strings.Contains(columnName, "id_card") || strings.Contains(columnName, "idcard") {
		return e.maskIDCard(strValue)
	}

	return value
}

// maskPhone 手机号脱敏
func (e *PostgreSQLEngine) maskPhone(phone string) string {
	if len(phone) >= 11 {
		return phone[:3] + "****" + phone[len(phone)-4:]
	}
	return phone
}

// maskEmail 邮箱脱敏
func (e *PostgreSQLEngine) maskEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return email
	}

	username := parts[0]
	domain := parts[1]

	if len(username) <= 2 {
		return "**@" + domain
	}

	return username[:1] + "***" + username[len(username)-1:] + "@" + domain
}

// maskName 姓名脱敏
func (e *PostgreSQLEngine) maskName(name string) string {
	runes := []rune(name)
	if len(runes) <= 1 {
		return "*"
	} else if len(runes) == 2 {
		return string(runes[0]) + "*"
	} else {
		return string(runes[0]) + strings.Repeat("*", len(runes)-2) + string(runes[len(runes)-1])
	}
}

// maskIDCard 身份证脱敏
func (e *PostgreSQLEngine) maskIDCard(idcard string) string {
	if len(idcard) == 18 {
		return idcard[:6] + "********" + idcard[14:]
	} else if len(idcard) == 15 {
		return idcard[:6] + "*****" + idcard[11:]
	}
	return idcard
}

// GetSecondsBehindMaster 获取主从延迟
func (e *PostgreSQLEngine) GetSecondsBehindMaster() int {
	// PostgreSQL没有主从延迟概念，返回0
	return 0
}

// KillConnection 终止连接
func (e *PostgreSQLEngine) KillConnection(threadID int) error {
	db, err := e.GetConnection("")
	if err != nil {
		return err
	}

	sql := fmt.Sprintf("SELECT pg_terminate_backend(%d)", threadID)
	_, err = db.Exec(sql)
	return err
}
