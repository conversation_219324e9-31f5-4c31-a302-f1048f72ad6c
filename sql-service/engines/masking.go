package engines

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/devops-microservices/sql-service/models"
	"gorm.io/gorm"
)

// MaskingEngine 数据脱敏引擎
type MaskingEngine struct {
	db *gorm.DB
}

// NewMaskingEngine 创建数据脱敏引擎
func NewMaskingEngine(db *gorm.DB) *MaskingEngine {
	return &MaskingEngine{
		db: db,
	}
}

// MaskingRule 脱敏规则
type MaskingRule struct {
	ColumnName string `json:"column_name"`
	RuleType   string `json:"rule_type"`
	RuleParams string `json:"rule_params,omitempty"`
}

// ApplyMasking 应用数据脱敏
func (m *MaskingEngine) ApplyMasking(instanceID uint, dbName, tableName string, queryResult *QueryResult) (*QueryResult, error) {
	// 获取脱敏配置
	var maskingConfig models.DataMaskingColumns
	err := m.db.Where("instance_id = ? AND table_schema = ? AND tb_name = ? AND active = ?",
		instanceID, dbName, tableName, true).First(&maskingConfig).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 没有脱敏配置，直接返回原结果
			return queryResult, nil
		}
		return nil, fmt.Errorf("获取脱敏配置失败: %w", err)
	}

	// 解析脱敏规则
	var rules []MaskingRule
	columnsBytes, err := json.Marshal(maskingConfig.Columns)
	if err != nil {
		return nil, fmt.Errorf("序列化脱敏配置失败: %w", err)
	}
	if err := json.Unmarshal(columnsBytes, &rules); err != nil {
		return nil, fmt.Errorf("解析脱敏规则失败: %w", err)
	}

	// 创建列名到索引的映射
	columnIndexMap := make(map[string]int)
	for i, columnName := range queryResult.Columns {
		columnIndexMap[strings.ToLower(columnName)] = i
	}

	// 应用脱敏规则
	maskedRows := make([][]interface{}, len(queryResult.Rows))
	for i, row := range queryResult.Rows {
		maskedRow := make([]interface{}, len(row))
		copy(maskedRow, row)

		for _, rule := range rules {
			columnIndex, exists := columnIndexMap[strings.ToLower(rule.ColumnName)]
			if exists && columnIndex < len(maskedRow) {
				maskedRow[columnIndex] = m.maskValue(maskedRow[columnIndex], rule)
			}
		}

		maskedRows[i] = maskedRow
	}

	// 返回脱敏后的结果
	maskedResult := &QueryResult{
		Columns:             queryResult.Columns,
		Rows:                maskedRows,
		AffectedRows:        queryResult.AffectedRows,
		QueryTime:           queryResult.QueryTime,
		SecondsBehindMaster: queryResult.SecondsBehindMaster,
		IsMasked:            true,
		MaskRuleHit:         true,
		MaskTime:            time.Now().Sub(time.Now()), // 计算脱敏耗时
	}

	return maskedResult, nil
}

// maskValue 对单个值进行脱敏
func (m *MaskingEngine) maskValue(value interface{}, rule MaskingRule) interface{} {
	if value == nil {
		return nil
	}

	strValue := fmt.Sprintf("%v", value)
	if strValue == "" {
		return value
	}

	switch rule.RuleType {
	case "phone":
		return m.maskPhone(strValue)
	case "email":
		return m.maskEmail(strValue)
	case "idcard":
		return m.maskIDCard(strValue)
	case "name":
		return m.maskName(strValue)
	case "address":
		return m.maskAddress(strValue)
	case "bankcard":
		return m.maskBankCard(strValue)
	case "password":
		return "******"
	case "hash":
		return m.hashValue(strValue)
	case "partial":
		return m.maskPartial(strValue, rule.RuleParams)
	default:
		// 默认脱敏：显示前2位和后2位，中间用*代替
		return m.maskDefault(strValue)
	}
}

// maskPhone 手机号脱敏
func (m *MaskingEngine) maskPhone(phone string) string {
	if len(phone) != 11 {
		return m.maskDefault(phone)
	}
	return phone[:3] + "****" + phone[7:]
}

// maskEmail 邮箱脱敏
func (m *MaskingEngine) maskEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return m.maskDefault(email)
	}

	username := parts[0]
	domain := parts[1]

	if len(username) <= 2 {
		return "**@" + domain
	}

	return username[:1] + "***" + username[len(username)-1:] + "@" + domain
}

// maskIDCard 身份证脱敏
func (m *MaskingEngine) maskIDCard(idcard string) string {
	if len(idcard) == 18 {
		return idcard[:6] + "********" + idcard[14:]
	} else if len(idcard) == 15 {
		return idcard[:6] + "*****" + idcard[11:]
	}
	return m.maskDefault(idcard)
}

// maskName 姓名脱敏
func (m *MaskingEngine) maskName(name string) string {
	runes := []rune(name)
	if len(runes) <= 1 {
		return "*"
	} else if len(runes) == 2 {
		return string(runes[0]) + "*"
	} else {
		return string(runes[0]) + strings.Repeat("*", len(runes)-2) + string(runes[len(runes)-1])
	}
}

// maskAddress 地址脱敏
func (m *MaskingEngine) maskAddress(address string) string {
	if len(address) <= 6 {
		return strings.Repeat("*", len(address))
	}
	return address[:3] + strings.Repeat("*", len(address)-6) + address[len(address)-3:]
}

// maskBankCard 银行卡脱敏
func (m *MaskingEngine) maskBankCard(bankcard string) string {
	if len(bankcard) < 8 {
		return m.maskDefault(bankcard)
	}
	return bankcard[:4] + strings.Repeat("*", len(bankcard)-8) + bankcard[len(bankcard)-4:]
}

// hashValue 哈希脱敏
func (m *MaskingEngine) hashValue(value string) string {
	hash := md5.Sum([]byte(value))
	return fmt.Sprintf("%x", hash)[:8]
}

// maskPartial 部分脱敏
func (m *MaskingEngine) maskPartial(value, params string) string {
	// params格式: "start:2,end:2" 表示保留前2位和后2位
	if params == "" {
		return m.maskDefault(value)
	}

	// 解析参数
	re := regexp.MustCompile(`start:(\d+),end:(\d+)`)
	matches := re.FindStringSubmatch(params)
	if len(matches) != 3 {
		return m.maskDefault(value)
	}

	start := parseInt(matches[1])
	end := parseInt(matches[2])

	if start+end >= len(value) {
		return strings.Repeat("*", len(value))
	}

	return value[:start] + strings.Repeat("*", len(value)-start-end) + value[len(value)-end:]
}

// maskDefault 默认脱敏
func (m *MaskingEngine) maskDefault(value string) string {
	if len(value) <= 4 {
		return strings.Repeat("*", len(value))
	}
	return value[:2] + strings.Repeat("*", len(value)-4) + value[len(value)-2:]
}

// CheckMaskingRules 检查是否需要脱敏
func (m *MaskingEngine) CheckMaskingRules(instanceID uint, dbName, tableName string) (bool, error) {
	var count int64
	err := m.db.Model(&models.DataMaskingColumns{}).Where(
		"instance_id = ? AND table_schema = ? AND tb_name = ? AND active = ?",
		instanceID, dbName, tableName, true,
	).Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// GetMaskingColumns 获取需要脱敏的列
func (m *MaskingEngine) GetMaskingColumns(instanceID uint, dbName, tableName string) ([]string, error) {
	var maskingConfig models.DataMaskingColumns
	err := m.db.Where("instance_id = ? AND table_schema = ? AND tb_name = ? AND active = ?",
		instanceID, dbName, tableName, true).First(&maskingConfig).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return []string{}, nil
		}
		return nil, err
	}

	var rules []MaskingRule
	columnsBytes, err := json.Marshal(maskingConfig.Columns)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(columnsBytes, &rules); err != nil {
		return nil, err
	}

	var columns []string
	for _, rule := range rules {
		columns = append(columns, rule.ColumnName)
	}

	return columns, nil
}
