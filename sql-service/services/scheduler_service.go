package services

import (
	"context"
	"fmt"
	"time"

	"github.com/devops-microservices/sql-service/config"
	"github.com/devops-microservices/sql-service/engines"
	"github.com/robfig/cron/v3"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// ScheduleTask 定时任务结构
type ScheduleTask struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"` // sql_execute, kill_connection, sync_user
	NextRun     time.Time              `json:"next_run"`
	Repeats     int                    `json:"repeats"` // -1表示无限重复，0表示不重复，>0表示重复次数
	Timeout     time.Duration          `json:"timeout"`
	Status      string                 `json:"status"` // pending, running, completed, failed
	Parameters  map[string]interface{} `json:"parameters"`
	CreatedTime time.Time              `json:"created_time"`
	UpdatedTime time.Time              `json:"updated_time"`
}

// SchedulerService 任务调度服务接口
type SchedulerService interface {
	// 任务管理
	AddSqlSchedule(name string, runDate time.Time, workflowID uint) error
	AddKillConnSchedule(name string, runDate time.Time, instanceID uint, threadID int) error
	AddSyncUserSchedule() error
	DeleteSchedule(name string) error
	GetTaskInfo(name string) (*ScheduleTask, error)

	// 调度器控制
	Start() error
	Stop() error
	IsRunning() bool

	// 任务执行
	ExecuteSqlWorkflow(workflowID uint) error
	KillQueryConnection(instanceID uint, threadID int) error
	SyncDingUsers() error
}

// SchedulerServiceImpl 任务调度服务实现
type SchedulerServiceImpl struct {
	cron      *cron.Cron
	tasks     map[string]*ScheduleTask
	db        *gorm.DB
	sqlSvc    SqlService
	config    *config.Config
	log       *logrus.Logger
	ctx       context.Context
	cancel    context.CancelFunc
	isRunning bool
}

// NewSchedulerService 创建任务调度服务
func NewSchedulerService(db *gorm.DB, sqlSvc SqlService, cfg *config.Config, log *logrus.Logger) SchedulerService {
	ctx, cancel := context.WithCancel(context.Background())

	// 设置时区
	location, err := time.LoadLocation(cfg.Scheduler.Timezone)
	if err != nil {
		log.Warnf("加载时区失败，使用默认时区: %v", err)
		location = time.Local
	}

	return &SchedulerServiceImpl{
		cron:   cron.New(cron.WithLocation(location), cron.WithSeconds()),
		tasks:  make(map[string]*ScheduleTask),
		db:     db,
		sqlSvc: sqlSvc,
		config: cfg,
		log:    log,
		ctx:    ctx,
		cancel: cancel,
	}
}

// Start 启动调度器
func (s *SchedulerServiceImpl) Start() error {
	if s.isRunning {
		return fmt.Errorf("调度器已经在运行")
	}

	s.cron.Start()
	s.isRunning = true
	s.log.Info("✅ 任务调度器启动成功")

	return nil
}

// Stop 停止调度器
func (s *SchedulerServiceImpl) Stop() error {
	if !s.isRunning {
		return nil
	}

	s.cancel()
	s.cron.Stop()
	s.isRunning = false
	s.log.Info("🛑 任务调度器已停止")

	return nil
}

// IsRunning 检查调度器是否运行
func (s *SchedulerServiceImpl) IsRunning() bool {
	return s.isRunning
}

// AddSqlSchedule 添加SQL定时执行任务
func (s *SchedulerServiceImpl) AddSqlSchedule(name string, runDate time.Time, workflowID uint) error {
	// 删除已存在的同名任务
	s.DeleteSchedule(name)

	task := &ScheduleTask{
		ID:      s.generateTaskID(),
		Name:    name,
		Type:    "sql_execute",
		NextRun: runDate,
		Repeats: 1,  // 只执行一次
		Timeout: -1, // 无超时限制
		Status:  "pending",
		Parameters: map[string]interface{}{
			"workflow_id": workflowID,
		},
		CreatedTime: time.Now(),
		UpdatedTime: time.Now(),
	}

	// 添加到cron调度器
	cronExpr := s.timeToCronExpr(runDate)
	entryID, err := s.cron.AddFunc(cronExpr, func() {
		s.executeSqlTask(task)
	})
	if err != nil {
		return fmt.Errorf("添加定时任务失败: %w", err)
	}

	task.ID = fmt.Sprintf("%d", entryID)
	s.tasks[name] = task

	s.log.Debugf("添加SQL定时执行任务：%s 执行时间：%s", name, runDate.Format("2006-01-02 15:04:05"))
	return nil
}

// AddKillConnSchedule 添加终止数据库连接的定时任务
func (s *SchedulerServiceImpl) AddKillConnSchedule(name string, runDate time.Time, instanceID uint, threadID int) error {
	// 删除已存在的同名任务
	s.DeleteSchedule(name)

	task := &ScheduleTask{
		ID:      s.generateTaskID(),
		Name:    name,
		Type:    "kill_connection",
		NextRun: runDate,
		Repeats: 1,  // 只执行一次
		Timeout: -1, // 无超时限制
		Status:  "pending",
		Parameters: map[string]interface{}{
			"instance_id": instanceID,
			"thread_id":   threadID,
		},
		CreatedTime: time.Now(),
		UpdatedTime: time.Now(),
	}

	// 添加到cron调度器
	cronExpr := s.timeToCronExpr(runDate)
	entryID, err := s.cron.AddFunc(cronExpr, func() {
		s.executeKillConnTask(task)
	})
	if err != nil {
		return fmt.Errorf("添加定时任务失败: %w", err)
	}

	task.ID = fmt.Sprintf("%d", entryID)
	s.tasks[name] = task

	s.log.Debugf("添加终止连接定时任务：%s 执行时间：%s", name, runDate.Format("2006-01-02 15:04:05"))
	return nil
}

// AddSyncUserSchedule 添加同步用户定时任务
func (s *SchedulerServiceImpl) AddSyncUserSchedule() error {
	name := "同步钉钉用户ID"

	// 删除已存在的同名任务
	s.DeleteSchedule(name)

	task := &ScheduleTask{
		ID:          s.generateTaskID(),
		Name:        name,
		Type:        "sync_user",
		NextRun:     time.Now().Add(time.Hour), // 1小时后开始
		Repeats:     -1,                        // 无限重复
		Timeout:     -1,                        // 无超时限制
		Status:      "pending",
		Parameters:  map[string]interface{}{},
		CreatedTime: time.Now(),
		UpdatedTime: time.Now(),
	}

	// 每天执行一次
	entryID, err := s.cron.AddFunc("0 0 2 * * *", func() { // 每天凌晨2点执行
		s.executeSyncUserTask(task)
	})
	if err != nil {
		return fmt.Errorf("添加定时任务失败: %w", err)
	}

	task.ID = fmt.Sprintf("%d", entryID)
	s.tasks[name] = task

	s.log.Debugf("添加同步用户定时任务：%s", name)
	return nil
}

// DeleteSchedule 删除定时任务
func (s *SchedulerServiceImpl) DeleteSchedule(name string) error {
	task, exists := s.tasks[name]
	if !exists {
		return nil // 任务不存在，直接返回
	}

	// 从cron调度器中移除
	if entryID, err := s.parseEntryID(task.ID); err == nil {
		s.cron.Remove(entryID)
	}

	// 从内存中删除
	delete(s.tasks, name)

	s.log.Debugf("删除定时任务：%s", name)
	return nil
}

// GetTaskInfo 获取定时任务详情
func (s *SchedulerServiceImpl) GetTaskInfo(name string) (*ScheduleTask, error) {
	task, exists := s.tasks[name]
	if !exists {
		return nil, fmt.Errorf("任务不存在: %s", name)
	}

	return task, nil
}

// ExecuteSqlWorkflow 执行SQL工单
func (s *SchedulerServiceImpl) ExecuteSqlWorkflow(workflowID uint) error {
	workflow, err := s.sqlSvc.GetSqlWorkflowByID(workflowID)
	if err != nil {
		return fmt.Errorf("获取SQL工单失败: %w", err)
	}

	// 检查工单状态
	if workflow.Status != "workflow_timingtask" {
		return fmt.Errorf("工单状态不允许定时执行: %s", workflow.Status)
	}

	// 更新工单状态为执行中
	if err := s.sqlSvc.UpdateSqlWorkflow(workflowID, map[string]interface{}{
		"status": "workflow_executing",
	}); err != nil {
		return fmt.Errorf("更新工单状态失败: %w", err)
	}

	// TODO: 实际执行SQL逻辑
	// 这里应该调用SQL引擎执行SQL语句

	s.log.Infof("定时执行SQL工单成功: %s", workflow.OrderID)
	return nil
}

// KillQueryConnection 终止查询连接
func (s *SchedulerServiceImpl) KillQueryConnection(instanceID uint, threadID int) error {
	instance, err := s.sqlSvc.GetDBInstanceByID(instanceID)
	if err != nil {
		return fmt.Errorf("获取数据库实例失败: %w", err)
	}

	engine, err := engines.GetEngine(instance)
	if err != nil {
		return fmt.Errorf("获取数据库引擎失败: %w", err)
	}
	defer engine.Close()

	if err := engine.KillConnection(threadID); err != nil {
		return fmt.Errorf("终止连接失败: %w", err)
	}

	s.log.Infof("终止数据库连接成功: instance=%s, thread=%d", instance.InstanceName, threadID)
	return nil
}

// SyncDingUsers 同步钉钉用户
func (s *SchedulerServiceImpl) SyncDingUsers() error {
	s.log.Info("开始同步钉钉用户...")

	// 这里可以调用钉钉API或其他用户系统API
	// 示例：从ucenter-service获取用户列表
	// 1. 调用用户中心API获取用户列表
	// 2. 更新本地用户缓存
	// 3. 同步用户权限信息

	// 暂时记录日志，实际实现需要根据具体的用户系统来定制
	s.log.Info("钉钉用户同步完成（当前为占位实现）")
	return nil
}

// 辅助方法

// generateTaskID 生成任务ID
func (s *SchedulerServiceImpl) generateTaskID() string {
	return fmt.Sprintf("task_%d", time.Now().UnixNano())
}

// timeToCronExpr 将时间转换为cron表达式（一次性执行）
func (s *SchedulerServiceImpl) timeToCronExpr(t time.Time) string {
	return fmt.Sprintf("%d %d %d %d %d *",
		t.Second(), t.Minute(), t.Hour(), t.Day(), int(t.Month()))
}

// parseEntryID 解析EntryID
func (s *SchedulerServiceImpl) parseEntryID(id string) (cron.EntryID, error) {
	var entryID cron.EntryID
	_, err := fmt.Sscanf(id, "%d", &entryID)
	return entryID, err
}

// 任务执行方法

// executeSqlTask 执行SQL任务
func (s *SchedulerServiceImpl) executeSqlTask(task *ScheduleTask) {
	task.Status = "running"
	task.UpdatedTime = time.Now()

	workflowID, ok := task.Parameters["workflow_id"].(uint)
	if !ok {
		s.log.Errorf("SQL任务参数错误: %s", task.Name)
		task.Status = "failed"
		return
	}

	if err := s.ExecuteSqlWorkflow(workflowID); err != nil {
		s.log.Errorf("执行SQL任务失败: %s, error: %v", task.Name, err)
		task.Status = "failed"
	} else {
		task.Status = "completed"
		s.log.Infof("SQL任务执行成功: %s", task.Name)
	}

	task.UpdatedTime = time.Now()

	// 如果是一次性任务，执行完成后删除
	if task.Repeats == 1 {
		s.DeleteSchedule(task.Name)
	}
}

// executeKillConnTask 执行终止连接任务
func (s *SchedulerServiceImpl) executeKillConnTask(task *ScheduleTask) {
	task.Status = "running"
	task.UpdatedTime = time.Now()

	instanceID, ok1 := task.Parameters["instance_id"].(uint)
	threadID, ok2 := task.Parameters["thread_id"].(int)
	if !ok1 || !ok2 {
		s.log.Errorf("终止连接任务参数错误: %s", task.Name)
		task.Status = "failed"
		return
	}

	if err := s.KillQueryConnection(instanceID, threadID); err != nil {
		s.log.Errorf("执行终止连接任务失败: %s, error: %v", task.Name, err)
		task.Status = "failed"
	} else {
		task.Status = "completed"
		s.log.Infof("终止连接任务执行成功: %s", task.Name)
	}

	task.UpdatedTime = time.Now()

	// 如果是一次性任务，执行完成后删除
	if task.Repeats == 1 {
		s.DeleteSchedule(task.Name)
	}
}

// executeSyncUserTask 执行同步用户任务
func (s *SchedulerServiceImpl) executeSyncUserTask(task *ScheduleTask) {
	task.Status = "running"
	task.UpdatedTime = time.Now()

	if err := s.SyncDingUsers(); err != nil {
		s.log.Errorf("执行同步用户任务失败: %s, error: %v", task.Name, err)
		task.Status = "failed"
	} else {
		task.Status = "completed"
		s.log.Infof("同步用户任务执行成功: %s", task.Name)
	}

	task.UpdatedTime = time.Now()

	// 重复任务不删除，状态重置为pending等待下次执行
	if task.Repeats == -1 {
		task.Status = "pending"
	}
}

// GetAllTasks 获取所有任务
func (s *SchedulerServiceImpl) GetAllTasks() map[string]*ScheduleTask {
	return s.tasks
}

// GetRunningTasks 获取正在运行的任务
func (s *SchedulerServiceImpl) GetRunningTasks() []*ScheduleTask {
	var runningTasks []*ScheduleTask
	for _, task := range s.tasks {
		if task.Status == "running" {
			runningTasks = append(runningTasks, task)
		}
	}
	return runningTasks
}

// GetTaskCount 获取任务数量统计
func (s *SchedulerServiceImpl) GetTaskCount() map[string]int {
	counts := map[string]int{
		"total":     0,
		"pending":   0,
		"running":   0,
		"completed": 0,
		"failed":    0,
	}

	for _, task := range s.tasks {
		counts["total"]++
		counts[task.Status]++
	}

	return counts
}
