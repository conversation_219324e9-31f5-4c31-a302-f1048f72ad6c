package engines

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/devops-microservices/sql-service/models"
	_ "github.com/mattn/go-sqlite3"
)

// SQLiteEngine SQLite数据库引擎
type SQLiteEngine struct {
	*BaseEngine
}

// NewSQLiteEngine 创建SQLite引擎
func NewSQLiteEngine(instance *models.DBInstance) *SQLiteEngine {
	return &SQLiteEngine{
		BaseEngine: NewBaseEngine(instance),
	}
}

// GetConnection 获取数据库连接
func (e *SQLiteEngine) GetConnection(dbName string) (*sql.DB, error) {
	if e.Connection != nil {
		return e.Connection, nil
	}

	dbPath := e.Instance.DBName
	if dbPath == "" {
		dbPath = "sql-service.db"
	}

	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, err
	}

	if err := db.<PERSON>(); err != nil {
		return nil, err
	}

	e.Connection = db
	return db, nil
}

// TestConnection 测试连接
func (e *SQLiteEngine) TestConnection() error {
	db, err := e.GetConnection("")
	if err != nil {
		return err
	}
	return db.Ping()
}

// Close 关闭连接
func (e *SQLiteEngine) Close() error {
	if e.Connection != nil {
		return e.Connection.Close()
	}
	return nil
}

// Query 执行查询
func (e *SQLiteEngine) Query(dbName, sql string, limitNum int, schemaName, tbName string, maxExecutionTime int) (*QueryResult, error) {
	db, err := e.GetConnection(dbName)
	if err != nil {
		return nil, err
	}

	// 添加LIMIT限制
	if limitNum > 0 && !strings.Contains(strings.ToUpper(sql), "LIMIT") {
		sql = BuildLimitSQL(sql, limitNum, "sqlite")
	}

	start := time.Now()
	rows, err := db.Query(sql)
	if err != nil {
		return &QueryResult{
			Error:     FormatError(err),
			QueryTime: time.Since(start),
		}, nil
	}
	defer rows.Close()

	data, columns, err := ConvertRowsToInterface(rows)
	if err != nil {
		return &QueryResult{
			Error:     FormatError(err),
			QueryTime: time.Since(start),
		}, nil
	}

	return &QueryResult{
		Columns:             columns,
		Rows:                data,
		AffectedRows:        int64(len(data)),
		QueryTime:           time.Since(start),
		SecondsBehindMaster: 0,
	}, nil
}

// QueryCheck 查询检查
func (e *SQLiteEngine) QueryCheck(dbName, sql string) map[string]interface{} {
	result := map[string]interface{}{
		"bad_query":    false,
		"has_star":     false,
		"filtered_sql": sql,
		"msg":          "",
	}

	// 检查是否包含SELECT *
	if strings.Contains(strings.ToUpper(sql), "SELECT *") {
		result["has_star"] = true
		result["msg"] = "查询语句包含SELECT *，可能影响性能"
	}

	return result
}

// FilterSQL 过滤SQL语句
func (e *SQLiteEngine) FilterSQL(sql string, limitNum int) string {
	return BuildLimitSQL(sql, limitNum, "sqlite")
}

// GetAllDatabases 获取所有数据库（SQLite只有一个数据库文件）
func (e *SQLiteEngine) GetAllDatabases() (*QueryResult, error) {
	return &QueryResult{
		Columns:      []string{"database"},
		Rows:         [][]interface{}{{"main"}},
		AffectedRows: 1,
	}, nil
}

// GetAllSchemas 获取所有模式（SQLite没有模式概念）
func (e *SQLiteEngine) GetAllSchemas(dbName string) (*QueryResult, error) {
	return &QueryResult{
		Columns:      []string{"schema"},
		Rows:         [][]interface{}{{"main"}},
		AffectedRows: 1,
	}, nil
}

// GetAllTables 获取所有表
func (e *SQLiteEngine) GetAllTables(dbName, schemaName string) (*QueryResult, error) {
	sql := "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
	return e.Query(dbName, sql, 0, "", "", 0)
}

// GetAllColumnsByTable 获取表的所有列
func (e *SQLiteEngine) GetAllColumnsByTable(dbName, tbName, schemaName string) (*QueryResult, error) {
	sql := fmt.Sprintf("PRAGMA table_info(%s)", tbName)
	return e.Query(dbName, sql, 0, "", "", 0)
}

// GetTableMetaData 获取表元数据
func (e *SQLiteEngine) GetTableMetaData(dbName, tbName string) map[string]interface{} {
	sql := fmt.Sprintf("SELECT COUNT(*) FROM %s", tbName)
	result, err := e.Query(dbName, sql, 0, "", "", 0)
	if err != nil || len(result.Rows) == 0 {
		return map[string]interface{}{"rows": []interface{}{tbName, "", "", 0}}
	}
	count := result.Rows[0][0]
	return map[string]interface{}{"rows": []interface{}{tbName, "sqlite", "", count}}
}

// GetTableDescData 获取表结构数据
func (e *SQLiteEngine) GetTableDescData(dbName, tbName, schemaName string) map[string]interface{} {
	result, err := e.GetAllColumnsByTable(dbName, tbName, schemaName)
	if err != nil {
		return map[string]interface{}{"error": err.Error()}
	}
	return map[string]interface{}{
		"column_list": result.Columns,
		"rows":        result.Rows,
	}
}

// GetTableIndexData 获取表索引数据
func (e *SQLiteEngine) GetTableIndexData(dbName, tbName string) map[string]interface{} {
	sql := fmt.Sprintf("PRAGMA index_list(%s)", tbName)
	result, err := e.Query(dbName, sql, 0, "", "", 0)
	if err != nil {
		return map[string]interface{}{"error": err.Error()}
	}
	return map[string]interface{}{
		"column_list": result.Columns,
		"rows":        result.Rows,
	}
}

// GetTableCreate 获取建表语句
func (e *SQLiteEngine) GetTableCreate(dbName, tbName, schemaName string) (*QueryResult, error) {
	sql := fmt.Sprintf("SELECT sql FROM sqlite_master WHERE type='table' AND name='%s'", tbName)
	return e.Query(dbName, sql, 0, "", "", 0)
}

// DescribeTable 描述表结构
func (e *SQLiteEngine) DescribeTable(dbName, tbName, schemaName string) (*QueryResult, error) {
	sql := fmt.Sprintf("PRAGMA table_info(%s)", tbName)
	return e.Query(dbName, sql, 0, "", "", 0)
}

// ExecuteCheck 执行检查
func (e *SQLiteEngine) ExecuteCheck(dbName, sql string) (*ReviewSet, error) {
	reviewSet := &ReviewSet{
		FullSQL:      sql,
		IsExecute:    false,
		Checked:      &[]bool{true}[0],
		WarningCount: 0,
		ErrorCount:   0,
		IsCritical:   false,
		SyntaxType:   ParseSQLType(sql),
		Rows: []ReviewResult{
			{
				ID:           1,
				Stage:        "CHECKED",
				ErrLevel:     0,
				StageStatus:  "Audit completed",
				ErrorMessage: "",
				SQL:          sql,
				AffectedRows: 0,
				Sequence:     "1",
			},
		},
		AffectedRows: 0,
	}
	return reviewSet, nil
}

// Execute 执行SQL
func (e *SQLiteEngine) Execute(dbName, sql string) (*ReviewSet, error) {
	return e.ExecuteCheck(dbName, sql)
}

// GetRollback 获取回滚语句
func (e *SQLiteEngine) GetRollback(workflow *models.SqlWorkflow) ([][]string, error) {
	return [][]string{}, nil
}

// QueryMasking 查询脱敏
func (e *SQLiteEngine) QueryMasking(dbName, sql string, queryResult *QueryResult) (*QueryResult, error) {
	return queryResult, nil
}

// GetSecondsBehindMaster 获取主从延迟
func (e *SQLiteEngine) GetSecondsBehindMaster() int {
	return 0
}

// KillConnection 终止连接
func (e *SQLiteEngine) KillConnection(threadID int) error {
	// SQLite不支持终止连接
	return fmt.Errorf("SQLite不支持终止连接操作")
}
