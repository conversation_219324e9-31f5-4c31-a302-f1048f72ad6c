# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
sql-service
sql-service-*

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
coverage.html

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Configuration files (keep examples)
config.yaml
!config.yaml.example

# Database files
*.db
*.sqlite
*.sqlite3

# Build directories
dist/
build/

# Temporary files
tmp/
temp/

# Docker volumes
postgres_data/
redis_data/
prometheus_data/
grafana_data/

# Backup files
*.bak
*.backup

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Certificate files
*.pem
*.key
*.crt
*.csr

# Monitoring data
monitoring/data/

# Test coverage
coverage.out

# Air (live reload) temporary files
tmp/

# Swagger generated files (keep docs.go)
docs/docs.go
docs/swagger.json
docs/swagger.yaml

# Local development files
local/
dev/

# Kubernetes manifests with secrets
k8s/*secret*.yaml
k8s/*configmap*.yaml

# Helm charts values
charts/*/values-*.yaml
!charts/*/values.yaml

# Terraform files
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Ansible files
*.retry

# Performance profiling files
*.prof
cpu.prof
mem.prof
block.prof
mutex.prof

# Debug files
debug
debug.test

# Vendor directory
vendor/

# Module cache
go.sum.backup
