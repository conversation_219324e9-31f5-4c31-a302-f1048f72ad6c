package controllers

import (
	"time"

	"github.com/devops-microservices/sql-service/models"
	"github.com/devops-microservices/sql-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// QueryController SQL查询控制器
type QueryController struct {
	*BaseController
	service services.SqlService
}

// NewQueryController 创建SQL查询控制器
func NewQueryController(service services.SqlService, log *logrus.Logger) *QueryController {
	return &QueryController{
		BaseController: NewBaseController(log),
		service:        service,
	}
}

// ExecuteQuery 执行SQL查询
// @Summary 执行SQL查询
// @Description 在指定数据库实例上执行SQL查询
// @Tags SQL查询
// @Accept json
// @Produce json
// @Param id path int true "实例ID"
// @Param query body models.SqlQueryRequest true "查询请求"
// @Success 200 {object} models.APIResponse{data=models.QueryResult}
// @Router /instance/{id}/query [post]
func (qc *QueryController) ExecuteQuery(c *gin.Context) {
	qc.LogRequest(c, "执行SQL查询")

	id, err := qc.GetIDParam(c)
	if err != nil {
		qc.BadRequestResponse(c, "无效的实例ID")
		return
	}

	var req models.SqlQueryRequest
	if !qc.ValidateJSON(c, &req) {
		return
	}

	// 检查实例是否存在
	instance, err := qc.service.GetDBInstanceByID(id)
	if err != nil {
		qc.NotFoundResponse(c, "数据库实例不存在")
		return
	}

	// 检查用户权限
	username := qc.GetCurrentUser(c)
	isAdmin := qc.IsAdmin(c)
	if !isAdmin && !qc.service.CheckUserQueryPrivilege(username, id, req.DBName) {
		qc.ErrorResponse(c, 403, "没有查询权限")
		return
	}

	// TODO: 实现实际的SQL查询逻辑
	// 这里应该使用SQL引擎来执行查询

	// 模拟查询结果
	result := &models.QueryResult{
		Columns:             []string{"id", "name", "created_at"},
		Rows:                [][]interface{}{{1, "test", "2023-01-01 00:00:00"}},
		AffectedRows:        1,
		QueryTime:           "0.001s",
		SecondsBehindMaster: 0,
	}

	// 记录查询日志
	queryLog := &models.SqlQueryLog{
		InstanceID: id,
		DBName:     req.DBName,
		SchemaName: req.SchemaName,
		SqlLog:     req.SqlContent,
		EffectRow:  result.AffectedRows,
		CostTime:   result.QueryTime,
		// DeployerID: TODO: 获取用户ID
		HitRule: false,
		Masking: false,
	}

	if _, err := qc.service.CreateSqlQueryLog(queryLog); err != nil {
		qc.log.Errorf("记录查询日志失败: %v", err)
	}

	qc.SuccessResponse(c, result)
}

// ExplainQuery 获取SQL执行计划
// @Summary 获取SQL执行计划
// @Description 获取SQL语句的执行计划
// @Tags SQL查询
// @Accept json
// @Produce json
// @Param id path int true "实例ID"
// @Param query body models.SqlQueryRequest true "查询请求"
// @Success 200 {object} models.APIResponse{data=models.QueryResult}
// @Router /instance/{id}/explain [post]
func (qc *QueryController) ExplainQuery(c *gin.Context) {
	qc.LogRequest(c, "获取SQL执行计划")

	id, err := qc.GetIDParam(c)
	if err != nil {
		qc.BadRequestResponse(c, "无效的实例ID")
		return
	}

	var req models.SqlQueryRequest
	if !qc.ValidateJSON(c, &req) {
		return
	}

	// 检查实例是否存在
	instance, err := qc.service.GetDBInstanceByID(id)
	if err != nil {
		qc.NotFoundResponse(c, "数据库实例不存在")
		return
	}

	// 检查用户权限
	username := qc.GetCurrentUser(c)
	isAdmin := qc.IsAdmin(c)
	if !isAdmin && !qc.service.CheckUserQueryPrivilege(username, id, req.DBName) {
		qc.ErrorResponse(c, 403, "没有查询权限")
		return
	}

	// 在SQL前添加EXPLAIN
	explainSQL := "EXPLAIN " + req.SqlContent

	// TODO: 实现实际的EXPLAIN查询逻辑
	// 这里应该使用SQL引擎来执行EXPLAIN查询

	// 模拟执行计划结果
	result := &models.QueryResult{
		Columns:      []string{"id", "select_type", "table", "type", "key", "rows", "Extra"},
		Rows:         [][]interface{}{{1, "SIMPLE", "test_table", "ALL", nil, 1000, "Using where"}},
		AffectedRows: 1,
		QueryTime:    "0.001s",
	}

	// 记录查询日志
	queryLog := &models.SqlQueryLog{
		InstanceID: id,
		DBName:     req.DBName,
		SchemaName: req.SchemaName,
		SqlLog:     explainSQL,
		EffectRow:  result.AffectedRows,
		CostTime:   result.QueryTime,
		// DeployerID: TODO: 获取用户ID
		HitRule: false,
		Masking: false,
	}

	if _, err := qc.service.CreateSqlQueryLog(queryLog); err != nil {
		qc.log.Errorf("记录查询日志失败: %v", err)
	}

	qc.SuccessResponse(c, result)
}

// GetQueryLogs 获取查询日志列表
// @Summary 获取查询日志列表
// @Description 获取SQL查询日志列表，支持分页和搜索
// @Tags SQL查询
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Param instance_id query int false "实例ID"
// @Success 200 {object} models.APIResponse{data=models.PaginatedResponse}
// @Router /querylog [get]
func (qc *QueryController) GetQueryLogs(c *gin.Context) {
	qc.LogRequest(c, "获取查询日志列表")

	page, pageSize := qc.GetPaginationParams(c)
	search := qc.GetSearchParam(c)

	var instanceID uint
	if instanceIDStr := c.Query("instance_id"); instanceIDStr != "" {
		if id, err := qc.parseUint(instanceIDStr); err == nil {
			instanceID = id
		}
	}

	logs, total, err := qc.service.GetSqlQueryLogs(page, pageSize, search, instanceID)
	if err != nil {
		qc.log.Errorf("获取查询日志列表失败: %v", err)
		qc.InternalErrorResponse(c, "获取查询日志列表失败")
		return
	}

	qc.PaginatedResponse(c, logs, total, page, pageSize)
}

// GetQueryLog 获取查询日志详情
// @Summary 获取查询日志详情
// @Description 根据ID获取查询日志详情
// @Tags SQL查询
// @Accept json
// @Produce json
// @Param id path int true "日志ID"
// @Success 200 {object} models.APIResponse{data=models.SqlQueryLog}
// @Router /querylog/{id} [get]
func (qc *QueryController) GetQueryLog(c *gin.Context) {
	qc.LogRequest(c, "获取查询日志详情")

	id, err := qc.GetIDParam(c)
	if err != nil {
		qc.BadRequestResponse(c, "无效的日志ID")
		return
	}

	log, err := qc.service.GetSqlQueryLogByID(id)
	if err != nil {
		qc.log.Errorf("获取查询日志详情失败: %v", err)
		qc.NotFoundResponse(c, "查询日志不存在")
		return
	}

	qc.SuccessResponse(c, log)
}

// GetTableInfo 获取表信息
// @Summary 获取表信息
// @Description 获取数据库表的元信息、结构、索引等
// @Tags SQL查询
// @Accept json
// @Produce json
// @Param id path int true "实例ID"
// @Param db_name query string true "数据库名"
// @Param table_name query string true "表名"
// @Param schema_name query string false "模式名"
// @Success 200 {object} models.APIResponse
// @Router /instance/{id}/table/info [get]
func (qc *QueryController) GetTableInfo(c *gin.Context) {
	qc.LogRequest(c, "获取表信息")

	id, err := qc.GetIDParam(c)
	if err != nil {
		qc.BadRequestResponse(c, "无效的实例ID")
		return
	}

	dbName := c.Query("db_name")
	tableName := c.Query("table_name")
	schemaName := c.Query("schema_name")

	if dbName == "" || tableName == "" {
		qc.BadRequestResponse(c, "数据库名和表名不能为空")
		return
	}

	// 检查实例是否存在
	instance, err := qc.service.GetDBInstanceByID(id)
	if err != nil {
		qc.NotFoundResponse(c, "数据库实例不存在")
		return
	}

	// TODO: 实现获取表信息的逻辑
	// 这里应该使用SQL引擎来获取表的元信息、结构、索引等

	// 模拟表信息
	tableInfo := map[string]interface{}{
		"meta_data": map[string]interface{}{
			"rows": []interface{}{tableName, "InnoDB", "utf8mb4_general_ci", 1000},
		},
		"desc": map[string]interface{}{
			"column_list": []string{"Field", "Type", "Null", "Key", "Default", "Extra"},
			"rows": [][]interface{}{
				{"id", "int(11)", "NO", "PRI", nil, "auto_increment"},
				{"name", "varchar(255)", "YES", "", nil, ""},
			},
		},
		"index": map[string]interface{}{
			"column_list": []string{"Table", "Non_unique", "Key_name", "Seq_in_index", "Column_name"},
			"rows": [][]interface{}{
				{tableName, 0, "PRIMARY", 1, "id"},
			},
		},
		"create_sql": [][]interface{}{
			{tableName, "CREATE TABLE `" + tableName + "` (\n  `id` int(11) NOT NULL AUTO_INCREMENT,\n  `name` varchar(255) DEFAULT NULL,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"},
		},
	}

	qc.SuccessResponse(c, tableInfo)
}

// parseUint 解析uint参数
func (qc *QueryController) parseUint(s string) (uint, error) {
	if s == "" {
		return 0, nil
	}
	id, err := qc.GetIDParam(&gin.Context{})
	return id, err
}
