<template>
  <div class="sql-instance-view">
    <el-card>
      <div class="toolbar">
        <el-input v-model="search" placeholder="搜索实例名称/别名/主机" style="width: 240px" @keyup.enter="fetchInstances" clearable />
        <el-button type="primary" icon="el-icon-plus" @click="openDialog()">新增实例</el-button>
        <el-button icon="el-icon-refresh" @click="fetchInstances">刷新</el-button>
      </div>
      <el-table :data="instances" v-loading="loading" style="width: 100%; margin-top: 16px;">
        <el-table-column prop="instance_name" label="实例名称" min-width="120" />
        <el-table-column prop="alias" label="别名" min-width="100" />
        <el-table-column prop="type" label="类型" min-width="80" />
        <el-table-column prop="db_type" label="DB类型" min-width="80" />
        <el-table-column prop="host" label="主机" min-width="120" />
        <el-table-column prop="port" label="端口" min-width="60" />
        <el-table-column prop="user" label="用户名" min-width="100" />
        <el-table-column prop="db_name" label="数据库名" min-width="100" />
        <el-table-column prop="charset" label="字符集" min-width="80" />
        <el-table-column prop="auto_backup" label="自动备份" min-width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.auto_backup" type="success">是</el-tag>
            <el-tag v-else type="info">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="140">
          <template #default="scope">
            <el-button size="mini" @click="openDialog(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        style="margin-top: 16px; text-align: right;"
        background
        layout="total, prev, pager, next, sizes"
        :total="total"
        :page-size="pageSize"
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        @current-change="val => { page = val; fetchInstances(); }"
        @size-change="val => { pageSize = val; page = 1; fetchInstances(); }"
      />
    </el-card>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px" @close="resetDialog">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="Instance Name" prop="instance_name">
          <el-input v-model="form.instance_name" autocomplete="off" />
        </el-form-item>
        <el-form-item label="Alias" prop="alias">
          <el-input v-model="form.alias" autocomplete="off" />
        </el-form-item>
        <el-form-item label="Address" prop="host">
          <el-input v-model="form.host" autocomplete="off" />
        </el-form-item>
        <el-form-item label="Port" prop="port">
          <el-input-number v-model="form.port" :min="1" :max="65535" controls-position="right" style="width: 180px;" />
        </el-form-item>
        <el-form-item label="Username" prop="user">
          <el-input v-model="form.user" autocomplete="off" />
        </el-form-item>
        <el-form-item label="Password" prop="password">
          <el-input v-model="form.password" autocomplete="off" show-password />
        </el-form-item>
        <el-form-item label="Type" prop="type">
          <el-select v-model="form.type" placeholder="请选择类型">
            <el-option label="Master" value="master" />
            <el-option label="Slave" value="slave" />
          </el-select>
        </el-form-item>
        <el-form-item label="DB Type" prop="db_type">
          <el-select v-model="form.db_type" placeholder="请选择数据库类型">
            <el-option label="MySQL" value="mysql" />
            <el-option label="PostgreSQL" value="postgres" />
            <el-option label="SQLite" value="sqlite" />
            <el-option label="MongoDB" value="mongodb" />
            <el-option label="Redis" value="redis" />
            <el-option label="Oracle" value="oracle" />
            <el-option label="MSSQL" value="mssql" />
            <el-option label="ClickHouse" value="clickhouse" />
          </el-select>
        </el-form-item>
        <el-form-item label="Mode" prop="mode">
          <el-select v-model="form.mode" placeholder="请选择运行模式">
            <el-option label="Standalone" value="standalone" />
            <el-option label="Cluster" value="cluster" />
          </el-select>
        </el-form-item>
        <el-form-item label="Environments" prop="environments">
          <el-select v-model="form.environments" multiple placeholder="请选择环境" style="width: 100%">
            <el-option label="DEV" value="DEV" />
            <el-option label="SIT" value="SIT" />
            <el-option label="UAT" value="UAT" />
            <el-option label="PROD" value="PROD" />
          </el-select>
        </el-form-item>
        <!-- 可根据后端模型补充更多字段，如projects等 -->
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="handleSubmit">{{ form.id ? 'Update' : 'Create' }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { sqlApi } from '../../api/modules/sql'
import { handleAPIResponse } from '../../utils/response'

const instances = ref([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(10)
const search = ref('')
const loading = ref(false)

const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogLoading = ref(false)
const formRef = ref(null)
const form = reactive({
  id: null,
  instance_name: '',
  alias: '',
  type: '',
  db_type: '',
  host: '',
  port: '',
  user: '',
  password: '',
  db_name: '',
  charset: '',
  auto_backup: 0,
  mode: '',
  environments: [],
  // projects: '',
})
const rules = {
  instance_name: [{ required: true, message: '必填', trigger: 'blur' }],
  host: [{ required: true, message: '必填', trigger: 'blur' }],
  port: [
    { required: true, message: '必填', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口范围1-65535', trigger: 'blur' }
  ],
  user: [{ required: true, message: '必填', trigger: 'blur' }],
  password: [{ required: true, message: '必填', trigger: 'blur' }],
  type: [{ required: true, message: '必选', trigger: 'change' }],
  db_type: [{ required: true, message: '必选', trigger: 'change' }],
  mode: [{ required: true, message: '必选', trigger: 'change' }],
  // environments: [{ type: 'array', required: true, message: '至少选择一个环境', trigger: 'change' }],
}

// 获取实例列表
async function fetchInstances() {
  loading.value = true
  try {
    const res = await sqlApi.getDBInstances({ page: page.value, pageSize: pageSize.value, search: search.value })
    const result = handleAPIResponse(res)
    instances.value = result.items || []
    total.value = result.total || 0
  } catch (e) {
    ElMessage.error('获取实例列表失败')
  } finally {
    loading.value = false
  }
}

// 打开弹窗（新增/编辑）
function openDialog(row = null) {
  if (row) {
    dialogTitle.value = '编辑实例'
    Object.assign(form, row)
  } else {
    dialogTitle.value = '新增实例'
    Object.assign(form, {
      id: null,
      instance_name: '',
      alias: '',
      type: '',
      db_type: '',
      host: '',
      port: '',
      user: '',
      password: '',
      db_name: '',
      charset: '',
      auto_backup: 0,
      mode: '',
      environments: [],
    })
  }
  dialogVisible.value = true
}

function resetDialog() {
  if (formRef.value) formRef.value.resetFields()
}

// 保存（新增/编辑）
async function handleSubmit() {
  await formRef.value.validate()
  const isEdit = !!form.id
  const confirmMsg = isEdit ? '确定要更新该数据库实例吗？' : '确定要创建该数据库实例吗？'
  ElMessageBox.confirm(confirmMsg, '确认操作', { type: 'warning' })
    .then(async () => {
      dialogLoading.value = true
      try {
        if (isEdit) {
          await sqlApi.updateDBInstance(form.id, form)
          ElMessage.success('更新成功')
        } else {
          await sqlApi.createDBInstance(form)
          ElMessage.success('创建成功')
        }
        dialogVisible.value = false
        fetchInstances()
      } catch (e) {
        ElMessage.error('保存失败')
      } finally {
        dialogLoading.value = false
      }
    })
    .catch(() => {})
}

// 删除
function handleDelete(row) {
  ElMessageBox.confirm(`确定要删除实例【${row.instance_name}】吗？`, '提示', {
    type: 'warning',
  }).then(async () => {
    try {
      await sqlApi.deleteDBInstance(row.id)
      ElMessage.success('删除成功')
      fetchInstances()
    } catch (e) {
      ElMessage.error('删除失败')
    }
  })
}

onMounted(() => {
  fetchInstances()
})
</script>

<style scoped>
.sql-instance-view {
  padding: 24px;
}
.toolbar {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}
</style>