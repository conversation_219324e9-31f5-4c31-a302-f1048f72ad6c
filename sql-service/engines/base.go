package engines

import (
	"database/sql"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/devops-microservices/sql-service/models"
)

// QueryResult 查询结果结构
type QueryResult struct {
	Columns             []string        `json:"column_list"`
	Rows                [][]interface{} `json:"rows"`
	AffectedRows        int64           `json:"affected_rows"`
	QueryTime           time.Duration   `json:"query_time"`
	SecondsBehindMaster int             `json:"seconds_behind_master"`
	Error               string          `json:"error,omitempty"`
	IsMasked            bool            `json:"is_masked"`
	MaskRuleHit         bool            `json:"mask_rule_hit"`
	MaskTime            time.Duration   `json:"mask_time"`
}

// ReviewResult 审核结果结构
type ReviewResult struct {
	ID           int    `json:"id"`
	Stage        string `json:"stage"`
	ErrLevel     int    `json:"errlevel"`
	StageStatus  string `json:"stagestatus"`
	ErrorMessage string `json:"errormessage"`
	SQL          string `json:"sql"`
	AffectedRows int64  `json:"affected_rows"`
	Sequence     string `json:"sequence"`
	BackupDBName string `json:"backup_dbname"`
	ExecuteTime  string `json:"execute_time"`
	SqlSha1      string `json:"sqlsha1"`
	BackupTime   string `json:"backup_time"`
}

// ReviewSet 审核结果集
type ReviewSet struct {
	FullSQL      string         `json:"full_sql"`
	IsExecute    bool           `json:"is_execute"`
	Checked      *bool          `json:"checked"`
	Warning      *string        `json:"warning"`
	Error        *string        `json:"error"`
	WarningCount int            `json:"warning_count"`
	ErrorCount   int            `json:"error_count"`
	IsCritical   bool           `json:"is_critical"`
	SyntaxType   int            `json:"syntax_type"`
	Rows         []ReviewResult `json:"rows"`
	Status       *string        `json:"status"`
	AffectedRows int64          `json:"affected_rows"`
}

// DatabaseEngine 数据库引擎接口
type DatabaseEngine interface {
	// 连接管理
	GetConnection(dbName string) (*sql.DB, error)
	TestConnection() error
	Close() error

	// 查询操作
	Query(dbName, sql string, limitNum int, schemaName, tbName string, maxExecutionTime int) (*QueryResult, error)
	QueryCheck(dbName, sql string) map[string]interface{}
	FilterSQL(sql string, limitNum int) string

	// 数据库信息获取
	GetAllDatabases() (*QueryResult, error)
	GetAllSchemas(dbName string) (*QueryResult, error)
	GetAllTables(dbName, schemaName string) (*QueryResult, error)
	GetAllColumnsByTable(dbName, tbName, schemaName string) (*QueryResult, error)

	// 表信息获取
	GetTableMetaData(dbName, tbName string) map[string]interface{}
	GetTableDescData(dbName, tbName, schemaName string) map[string]interface{}
	GetTableIndexData(dbName, tbName string) map[string]interface{}
	GetTableCreate(dbName, tbName, schemaName string) (*QueryResult, error)
	DescribeTable(dbName, tbName, schemaName string) (*QueryResult, error)

	// SQL执行和审核
	ExecuteCheck(dbName, sql string) (*ReviewSet, error)
	Execute(dbName, sql string) (*ReviewSet, error)
	GetRollback(workflow *models.SqlWorkflow) ([][]string, error)

	// 数据脱敏
	QueryMasking(dbName, sql string, queryResult *QueryResult) (*QueryResult, error)

	// 其他功能
	GetSecondsBehindMaster() int
	KillConnection(threadID int) error
}

// BaseEngine 基础引擎结构
type BaseEngine struct {
	Instance   *models.DBInstance
	Connection *sql.DB
	ThreadID   int
}

// NewBaseEngine 创建基础引擎
func NewBaseEngine(instance *models.DBInstance) *BaseEngine {
	return &BaseEngine{
		Instance: instance,
	}
}

// GetEngineType 获取引擎类型
func GetEngineType(dbType string) string {
	switch dbType {
	case "mysql":
		return "mysql"
	case "postgres", "postgresql":
		return "postgres"
	case "sqlite":
		return "sqlite"
	case "mongodb", "mongo":
		return "mongodb"
	case "redis":
		return "redis"
	case "oracle":
		return "oracle"
	case "mssql", "sqlserver":
		return "mssql"
	case "clickhouse":
		return "clickhouse"
	default:
		return "unknown"
	}
}

// GetEngine 获取数据库引擎实例
func GetEngine(instance *models.DBInstance) (DatabaseEngine, error) {
	engineType := GetEngineType(instance.DBType)

	switch engineType {
	case "mysql":
		return NewMySQLEngine(instance), nil
	case "postgres":
		return NewPostgreSQLEngine(instance), nil
	case "sqlite":
		return NewSQLiteEngine(instance), nil
	case "mongodb":
		return NewMongoDBEngine(instance), nil
	case "redis":
		return NewRedisEngine(instance), nil
	default:
		return nil, fmt.Errorf("不支持的数据库类型: %s", instance.DBType)
	}
}

// ValidateSQL 验证SQL语句
func ValidateSQL(sql string) error {
	if sql == "" {
		return fmt.Errorf("SQL语句不能为空")
	}

	// 清理SQL语句
	cleanSQL := strings.TrimSpace(strings.ToUpper(sql))

	// 检查危险操作
	dangerousPatterns := []string{
		"DROP DATABASE",
		"DROP SCHEMA",
		"TRUNCATE TABLE",
		"DELETE FROM.*WHERE.*1.*=.*1",
		"UPDATE.*SET.*WHERE.*1.*=.*1",
		"GRANT",
		"REVOKE",
		"CREATE USER",
		"DROP USER",
		"ALTER USER",
	}

	for _, pattern := range dangerousPatterns {
		if matched, _ := regexp.MatchString(pattern, cleanSQL); matched {
			return fmt.Errorf("包含危险操作: %s", pattern)
		}
	}

	// 检查基本语法
	if !strings.HasSuffix(cleanSQL, ";") &&
		!strings.Contains(cleanSQL, "SELECT") &&
		!strings.Contains(cleanSQL, "SHOW") &&
		!strings.Contains(cleanSQL, "EXPLAIN") {
		// 对于非查询语句，建议以分号结尾
	}

	return nil
}

// SanitizeSQL 清理SQL语句
func SanitizeSQL(sql string) string {
	// 移除多行注释
	re := regexp.MustCompile(`/\*.*?\*/`)
	sql = re.ReplaceAllString(sql, "")

	// 移除单行注释
	lines := strings.Split(sql, "\n")
	var cleanLines []string
	for _, line := range lines {
		// 移除 -- 注释
		if idx := strings.Index(line, "--"); idx != -1 {
			line = line[:idx]
		}
		// 移除 # 注释
		if idx := strings.Index(line, "#"); idx != -1 {
			line = line[:idx]
		}
		line = strings.TrimSpace(line)
		if line != "" {
			cleanLines = append(cleanLines, line)
		}
	}

	// 重新组合
	sql = strings.Join(cleanLines, " ")

	// 移除多余的空格
	re = regexp.MustCompile(`\s+`)
	sql = re.ReplaceAllString(sql, " ")

	return strings.TrimSpace(sql)
}

// ParseSQLType 解析SQL类型
func ParseSQLType(sql string) int {
	// 0: 其他
	// 1: DDL (CREATE, ALTER, DROP)
	// 2: DML (INSERT, UPDATE, DELETE, SELECT)

	cleanSQL := strings.TrimSpace(strings.ToUpper(sql))

	// DDL语句
	ddlKeywords := []string{"CREATE", "ALTER", "DROP", "TRUNCATE"}
	for _, keyword := range ddlKeywords {
		if strings.HasPrefix(cleanSQL, keyword+" ") {
			return 1
		}
	}

	// DML语句
	dmlKeywords := []string{"SELECT", "INSERT", "UPDATE", "DELETE", "REPLACE"}
	for _, keyword := range dmlKeywords {
		if strings.HasPrefix(cleanSQL, keyword+" ") {
			return 2
		}
	}

	// 其他语句（如SHOW, EXPLAIN, SET等）
	return 0
}

// BuildLimitSQL 构建带限制的SQL
func BuildLimitSQL(sql string, limitNum int, dbType string) string {
	if limitNum <= 0 {
		return sql
	}

	switch dbType {
	case "mysql", "postgres", "sqlite":
		return sql + fmt.Sprintf(" LIMIT %d", limitNum)
	case "mssql":
		return fmt.Sprintf("SELECT TOP %d * FROM (%s) AS limited_query", limitNum, sql)
	case "oracle":
		return fmt.Sprintf("SELECT * FROM (%s) WHERE ROWNUM <= %d", sql, limitNum)
	default:
		return sql
	}
}

// FormatError 格式化错误信息
func FormatError(err error) string {
	if err == nil {
		return ""
	}
	return err.Error()
}

// ConvertRowsToInterface 转换数据库行为interface{}切片
func ConvertRowsToInterface(rows *sql.Rows) ([][]interface{}, []string, error) {
	columns, err := rows.Columns()
	if err != nil {
		return nil, nil, err
	}

	var result [][]interface{}

	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))

		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, nil, err
		}

		// 转换字节数组为字符串
		for i, val := range values {
			if b, ok := val.([]byte); ok {
				values[i] = string(b)
			}
		}

		result = append(result, values)
	}

	return result, columns, nil
}

// GetConnectionString 获取连接字符串
func GetConnectionString(instance *models.DBInstance) string {
	switch instance.DBType {
	case "mysql":
		return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
			instance.User, instance.Password, instance.Host, instance.Port, instance.DBName, instance.Charset)
	case "postgres":
		return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
			instance.Host, instance.Port, instance.User, instance.Password, instance.DBName)
	case "sqlite":
		return instance.DBName
	default:
		return ""
	}
}
