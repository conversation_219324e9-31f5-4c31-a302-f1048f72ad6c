<template>
    <div class="sql-query-container">
        <!-- 顶部工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <el-select v-model="selectedInstance" placeholder="选择数据库实例" style="width: 200px"
                    @change="onInstanceChange">
                    <el-option v-for="instance in instances" :key="instance.id"
                        :label="instance.alias || instance.instance_name" :value="instance.id" />
                </el-select>

                <el-select v-model="selectedDatabase" placeholder="选择数据库" style="width: 150px; margin-left: 10px"
                    :disabled="!selectedInstance" @change="onDatabaseChange">
                    <el-option v-for="db in databases" :key="db.database_name" :label="db.database_name"
                        :value="db.database_name" />
                </el-select>

                <el-button type="primary" :icon="VideoPlay" :loading="queryLoading"
                    :disabled="!selectedInstance || !selectedDatabase || !sqlContent.trim()" @click="executeQuery"
                    style="margin-left: 10px">
                    执行查询
                </el-button>

                <el-button :icon="Document" :disabled="!selectedInstance || !selectedDatabase || !sqlContent.trim()"
                    @click="explainQuery">
                    执行计划
                </el-button>

                <el-button :icon="Refresh" @click="clearResult">
                    清空结果
                </el-button>
            </div>

            <div class="toolbar-right">
                <el-button :icon="Download" :disabled="!queryResult || !queryResult.rows.length"
                    @click="downloadResult">
                    导出结果
                </el-button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧数据库结构树 -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <span>数据库实例</span>
                    <el-button :icon="Refresh" size="small" text @click="refreshDatabaseTree" />
                </div>

                <div class="database-tree">
                    <el-tree :data="databaseTree" :props="treeProps" node-key="id" :expand-on-click-node="false"
                        @node-click="onTreeNodeClick">
                        <template #default="{ node, data }">
                            <span class="tree-node">
                                <el-icon v-if="data.type === 'instance'">
                                    <Connection />
                                </el-icon>
                                <el-icon v-else-if="data.type === 'database'">
                                    <Folder />
                                </el-icon>
                                <el-icon v-else-if="data.type === 'table'">
                                    <Document />
                                </el-icon>
                                <span class="node-label">{{ node.label }}</span>
                            </span>
                        </template>
                    </el-tree>
                </div>
            </div>

            <!-- 右侧查询区域 -->
            <div class="query-area">
                <!-- SQL编辑器 -->
                <div class="sql-editor">
                    <div class="editor-header">
                        <span>SQL查询</span>
                        <div class="editor-actions">
                            <el-button size="small" text @click="formatSql">格式化</el-button>
                            <el-button size="small" text @click="clearSql">清空</el-button>
                        </div>
                    </div>

                    <div class="editor-content">
                        <el-input v-model="sqlContent" type="textarea" :rows="8" placeholder="请输入SQL查询语句..."
                            resize="vertical" />
                    </div>
                </div>

                <!-- 查询结果 -->
                <div class="result-area">
                    <div class="result-header">
                        <span>查询结果</span>
                        <div class="result-info" v-if="queryResult">
                            <span>影响行数: {{ queryResult.affected_rows }}</span>
                            <span style="margin-left: 20px">查询时间: {{ queryResult.query_time }}ms</span>
                        </div>
                    </div>

                    <div class="result-content">
                        <el-alert v-if="queryError" :title="queryError" type="error" show-icon :closable="false" />

                        <el-table v-if="queryResult && queryResult.rows.length > 0" :data="tableData" border stripe
                            height="400" style="width: 100%">
                            <el-table-column v-for="(column, index) in queryResult.column_list" :key="index"
                                :prop="`col_${index}`" :label="column" min-width="120" show-overflow-tooltip />
                        </el-table>

                        <el-empty v-if="!queryResult || queryResult.rows.length === 0" description="暂无查询结果" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
    VideoPlay,
    Document,
    Refresh,
    Download,
    Folder,
    Connection
} from '@element-plus/icons-vue'
import { sqlApi, type DBInstance, type DatabaseInfo, type QueryResult, type SqlQueryRequest } from '../../api/modules/sql'
import { handleAPIResponse } from '@/utils/response'

// 响应式数据
const selectedInstance = ref<number | null>(null)
const selectedDatabase = ref<string>('')
const instances = ref<DBInstance[]>([])
const databases = ref<DatabaseInfo[]>([])
const sqlContent = ref<string>('')
const queryResult = ref<QueryResult | null>(null)
const queryError = ref<string>('')
const queryLoading = ref<boolean>(false)

// 数据库树相关
const databaseTree = ref<any[]>([])
const treeProps = {
    children: 'children',
    label: 'label'
}

// 计算属性 - 表格数据
const tableData = computed(() => {
    if (!queryResult.value || !queryResult.value.rows.length) {
        return []
    }

    return queryResult.value.rows.map((row: any[]) => {
        const obj: any = {}
        row.forEach((cell: any, index: number) => {
            obj[`col_${index}`] = cell
        })
        return obj
    })
})

// 生命周期
onMounted(() => {
    loadInstances()
})

// 方法
const loadInstances = async () => {
    try {
        const response = await sqlApi.getDBInstances()
        const result = handleAPIResponse(response) as any
        console.log(result, '@@@@@@@')
        if (result && result.items) {
            instances.value = result.items || []
        }
    } catch (error) {
        console.error('加载数据库实例失败:', error)
        ElMessage.error('加载数据库实例失败')
    }
}

const onInstanceChange = async (instanceId: number) => {
    selectedDatabase.value = ''
    databases.value = []
    queryResult.value = null
    queryError.value = ''

    if (instanceId) {
        await loadDatabases(instanceId)
        await loadDatabaseTree(instanceId)
    }
}

const loadDatabases = async (instanceId: number) => {
    try {
        const response = await sqlApi.getDatabases(instanceId)
        if (response.data && response.data.data) {
            databases.value = response.data.data || []
        }
    } catch (error) {
        console.error('加载数据库列表失败:', error)
        ElMessage.error('加载数据库列表失败')
    }
}

const onDatabaseChange = () => {
    queryResult.value = null
    queryError.value = ''
}

const executeQuery = async () => {
    if (!selectedInstance.value || !selectedDatabase.value || !sqlContent.value.trim()) {
        ElMessage.warning('请选择数据库实例和数据库，并输入SQL语句')
        return
    }

    queryLoading.value = true
    queryError.value = ''

    try {
        const queryRequest: SqlQueryRequest = {
            sql_content: sqlContent.value.trim(),
            db_name: selectedDatabase.value,
            limit_num: 1000
        }

        const response = await sqlApi.executeQuery(selectedInstance.value, queryRequest)
        if (response.data && response.data.data) {
            queryResult.value = response.data.data
            if (response.data.data.error) {
                queryError.value = response.data.data.error
            } else {
                ElMessage.success(`查询成功，返回 ${response.data.data.affected_rows} 行数据`)
            }
        }
    } catch (error: any) {
        console.error('执行查询失败:', error)
        queryError.value = error.message || '执行查询失败'
        ElMessage.error('执行查询失败')
    } finally {
        queryLoading.value = false
    }
}

const explainQuery = async () => {
    if (!selectedInstance.value || !selectedDatabase.value || !sqlContent.value.trim()) {
        ElMessage.warning('请选择数据库实例和数据库，并输入SQL语句')
        return
    }

    try {
        const queryRequest: SqlQueryRequest = {
            sql_content: sqlContent.value.trim(),
            db_name: selectedDatabase.value
        }

        const response = await sqlApi.explainQuery(selectedInstance.value, queryRequest)
        if (response.data && response.data.data) {
            queryResult.value = response.data.data
            ElMessage.success('获取执行计划成功')
        }
    } catch (error: any) {
        console.error('获取执行计划失败:', error)
        queryError.value = error.message || '获取执行计划失败'
        ElMessage.error('获取执行计划失败')
    }
}

const clearResult = () => {
    queryResult.value = null
    queryError.value = ''
}

const clearSql = () => {
    sqlContent.value = ''
}

const formatSql = () => {
    // 简单的SQL格式化
    if (sqlContent.value.trim()) {
        const formatted = sqlContent.value
            .replace(/\s+/g, ' ')
            .replace(/\s*,\s*/g, ',\n  ')
            .replace(/\s*(SELECT|FROM|WHERE|GROUP BY|ORDER BY|HAVING|LIMIT)\s+/gi, '\n$1 ')
            .replace(/\s*(AND|OR)\s+/gi, '\n  $1 ')
            .trim()

        sqlContent.value = formatted
    }
}

const downloadResult = () => {
    if (!queryResult.value || !queryResult.value.rows.length) {
        ElMessage.warning('没有可导出的数据')
        return
    }

    // 这里可以实现CSV导出功能
    ElMessage.info('导出功能开发中...')
}

// 加载数据库树结构
const loadDatabaseTree = async (instanceId: number) => {
    try {
        const instance = instances.value.find(inst => inst.id === instanceId)
        if (!instance) return

        const response = await sqlApi.getDatabases(instanceId)
        if (response.data && response.data.data) {
            const databases = response.data.data

            const treeData = [{
                id: `instance_${instanceId}`,
                label: instance.alias || instance.instance_name,
                type: 'instance',
                children: databases.map((db: any) => ({
                    id: `db_${instanceId}_${db.database_name}`,
                    label: db.database_name,
                    type: 'database',
                    instanceId,
                    dbName: db.database_name,
                    children: []
                }))
            }]

            databaseTree.value = treeData
        }
    } catch (error) {
        console.error('加载数据库树失败:', error)
    }
}

// 刷新数据库树
const refreshDatabaseTree = () => {
    if (selectedInstance.value) {
        loadDatabaseTree(selectedInstance.value)
    }
}

// 树节点点击事件
const onTreeNodeClick = async (data: any) => {
    if (data.type === 'database') {
        selectedInstance.value = data.instanceId
        selectedDatabase.value = data.dbName

        // 加载表列表
        try {
            const response = await sqlApi.getTables(data.instanceId, { db_name: data.dbName })
            if (response.data && response.data.data) {
                const tables = response.data.data

                // 更新树结构，添加表节点
                const updateTreeNode = (nodes: any[]): any[] => {
                    return nodes.map(node => {
                        if (node.id === data.id) {
                            return {
                                ...node,
                                children: tables.map((table: string) => ({
                                    id: `table_${data.instanceId}_${data.dbName}_${table}`,
                                    label: table,
                                    type: 'table',
                                    instanceId: data.instanceId,
                                    dbName: data.dbName,
                                    tableName: table
                                }))
                            }
                        } else if (node.children) {
                            return {
                                ...node,
                                children: updateTreeNode(node.children)
                            }
                        }
                        return node
                    })
                }

                databaseTree.value = updateTreeNode(databaseTree.value)
            }
        } catch (error) {
            console.error('加载表列表失败:', error)
        }
    } else if (data.type === 'table') {
        // 点击表时，生成SELECT语句
        sqlContent.value = `SELECT * FROM ${data.tableName} LIMIT 100;`
    }
}
</script>

<style scoped>
.sql-query-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: white;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
    display: flex;
    align-items: center;
}

.toolbar-right {
    display: flex;
    align-items: center;
}

.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.sidebar {
    width: 280px;
    background-color: white;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e4e7ed;
    font-weight: 600;
    color: #303133;
}

.database-tree {
    flex: 1;
    padding: 8px;
    overflow-y: auto;
}

.tree-node {
    display: flex;
    align-items: center;
    gap: 8px;
}

.node-label {
    font-size: 14px;
    color: #606266;
}

.query-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px;
    gap: 16px;
}

.sql-editor {
    background-color: white;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    overflow: hidden;
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;
    font-weight: 600;
    color: #303133;
}

.editor-actions {
    display: flex;
    gap: 8px;
}

.editor-content {
    padding: 16px;
}

.result-area {
    flex: 1;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;
    font-weight: 600;
    color: #303133;
}

.result-info {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #909399;
    font-weight: normal;
}

.result-content {
    flex: 1;
    padding: 16px;
    overflow: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .sidebar {
        width: 240px;
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: 200px;
    }

    .toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .toolbar-left,
    .toolbar-right {
        justify-content: center;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .sql-query-container {
        background-color: #1a1a1a;
    }

    .toolbar,
    .sidebar,
    .sql-editor,
    .result-area {
        background-color: #2d2d2d;
        border-color: #404040;
    }

    .sidebar-header,
    .editor-header,
    .result-header {
        background-color: #3a3a3a;
        border-color: #404040;
        color: #e4e7ed;
    }

    .node-label {
        color: #c0c4cc;
    }

    .result-info {
        color: #909399;
    }
}
</style>