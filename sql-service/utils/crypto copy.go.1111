package utils

import (
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"log"

	"github.com/fernet/fernet-go"
	"golang.org/x/crypto/hkdf"
)

// EncryptedField 加密字段处理器
type EncryptedField struct {
	fernetKeys []*fernet.Key
	salt       []byte
	info       []byte
}

// NewEncryptedField 创建新的加密字段处理器
func NewEncryptedField(keys []string, salt, info string) (*EncryptedField, error) {
	if len(keys) == 0 {
		return nil, errors.New("at least one key is required")
	}

	ef := &EncryptedField{
		salt: []byte(salt),
		info: []byte(info),
	}

	// 为每个密钥创建Fernet密钥
	for _, key := range keys {
		derivedKey := ef.deriveFernetKey([]byte(key))
		fernetKey, err := fernet.DecodeKey(derivedKey)
		if err != nil {
			return nil, fmt.Errorf("failed to decode key: %v", err)
		}
		ef.fernetKeys = append(ef.fernetKeys, fernetKey)
	}

	return ef, nil
}

// deriveFernetKey 使用 HKDF 派生密钥
func (ef *EncryptedField) deriveFernetKey(inputKey []byte) string {
	// 使用 HKDF 派生密钥
	hash := sha256.New
	kdf := hkdf.New(hash, inputKey, ef.salt, ef.info)
	derivedKey := make([]byte, 32)
	if _, err := kdf.Read(derivedKey); err != nil {
		log.Fatal(err)
	}

	// 将派生密钥编码为 URL 安全的 Base64
	return base64.URLEncoding.EncodeToString(derivedKey)
}

// Encrypt 加密数据
func (ef *EncryptedField) Encrypt(data []byte) ([]byte, error) {
	if len(ef.fernetKeys) == 0 {
		return nil, errors.New("no fernet keys available")
	}

	token, err := fernet.EncryptAndSign(data, ef.fernetKeys[0])
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt data: %v", err)
	}

	return token, nil
}

// Decrypt 解密数据
func (ef *EncryptedField) Decrypt(token []byte) ([]byte, error) {
	if len(ef.fernetKeys) == 0 {
		return nil, errors.New("no fernet keys available")
	}

	data := fernet.VerifyAndDecrypt(token, 0, ef.fernetKeys)
	if data == nil {
		return nil, errors.New("failed to decrypt data")
	}

	return data, nil
}

// EncryptString 加密字符串
func (ef *EncryptedField) EncryptString(plaintext string) (string, error) {
	if plaintext == "" {
		return "", nil
	}

	encrypted, err := ef.Encrypt([]byte(plaintext))
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(encrypted), nil
}

// DecryptString 解密字符串
func (ef *EncryptedField) DecryptString(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}

	// Base64解码
	encrypted, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %v", err)
	}

	// 解密
	decrypted, err := ef.Decrypt(encrypted)
	if err != nil {
		return "", err
	}

	return string(decrypted), nil
}

// 全局加密实例
var globalEncryptedField *EncryptedField

// InitEncryption 初始化全局加密实例
func InitEncryption(secretKey, salt, info string) error {
	var err error
	globalEncryptedField, err = NewEncryptedField([]string{secretKey}, salt, info)
	return err
}

// EncryptData 使用全局实例加密数据
func EncryptData(plaintext string) (string, error) {
	if globalEncryptedField == nil {
		return "", errors.New("encryption not initialized")
	}
	return globalEncryptedField.EncryptString(plaintext)
}

// DecryptData 使用全局实例解密数据
func DecryptData(ciphertext string) (string, error) {
	if globalEncryptedField == nil {
		return "", errors.New("encryption not initialized")
	}
	return globalEncryptedField.DecryptString(ciphertext)
}
