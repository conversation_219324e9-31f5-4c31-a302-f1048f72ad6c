<template>
  <div ref="editorContainer" class="monaco-editor-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import * as monaco from 'monaco-editor'
import loader from '@monaco-editor/loader'

// Props定义
interface Props {
  modelValue?: string
  value?: string
  language?: string
  theme?: string
  height?: string | number
  width?: string | number
  options?: monaco.editor.IStandaloneEditorConstructionOptions
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  language: 'sql',
  theme: 'vs-dark',
  height: '300px',
  width: '100%',
  readonly: false,
  options: () => ({})
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string]
  'focus': []
  'blur': []
}>()

// 响应式数据
const editorContainer = ref<HTMLElement>()
let editor: monaco.editor.IStandaloneCodeEditor | null = null

// 获取当前值
const getCurrentValue = () => props.modelValue || props.value || ''

// 默认编辑器选项
const defaultOptions: monaco.editor.IStandaloneEditorConstructionOptions = {
  automaticLayout: true,
  fontSize: 14,
  lineNumbers: 'on',
  roundedSelection: false,
  scrollBeyondLastLine: false,
  readOnly: props.readonly,
  minimap: { enabled: false },
  wordWrap: 'on',
  folding: true,
  foldingHighlight: true,
  foldingStrategy: 'indentation',
  showFoldingControls: 'always',
  unfoldOnClickAfterEndOfLine: false,
  contextmenu: true,
  mouseWheelZoom: true,
  smoothScrolling: true,
  cursorBlinking: 'blink',
  cursorSmoothCaretAnimation: 'on',
  renderWhitespace: 'selection',
  renderControlCharacters: false,
  fontLigatures: true,
  suggest: {
    showKeywords: true,
    showSnippets: true,
    showFunctions: true,
  },
  quickSuggestions: {
    other: true,
    comments: false,
    strings: false
  }
}

// 初始化编辑器
const initEditor = async () => {
  if (!editorContainer.value) return

  try {
    // 配置Monaco Editor
    loader.config({
      paths: {
        vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs'
      }
    })

    const monacoInstance = await loader.init()

    // 合并选项
    const editorOptions = {
      ...defaultOptions,
      ...props.options,
      value: getCurrentValue(),
      language: props.language,
      theme: props.theme,
      readOnly: props.readonly
    }

    // 创建编辑器实例
    editor = monacoInstance.editor.create(editorContainer.value, editorOptions)

    // 设置编辑器大小
    updateEditorSize()

    // 监听内容变化
    editor.onDidChangeModelContent(() => {
      const value = editor?.getValue() || ''
      emit('update:modelValue', value)
      emit('change', value)
    })

    // 监听焦点事件
    editor.onDidFocusEditorText(() => {
      emit('focus')
    })

    editor.onDidBlurEditorText(() => {
      emit('blur')
    })

    // 添加SQL关键字高亮
    if (props.language === 'sql') {
      setupSQLLanguage(monacoInstance)
    }

  } catch (error) {
    console.error('Monaco Editor initialization failed:', error)
  }
}

// 设置SQL语言支持
const setupSQLLanguage = (monacoInstance: typeof monaco) => {
  // SQL关键字
  const sqlKeywords = [
    'SELECT', 'FROM', 'WHERE', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER',
    'TABLE', 'INDEX', 'VIEW', 'DATABASE', 'SCHEMA', 'PROCEDURE', 'FUNCTION', 'TRIGGER',
    'AND', 'OR', 'NOT', 'IN', 'EXISTS', 'BETWEEN', 'LIKE', 'IS', 'NULL', 'TRUE', 'FALSE',
    'JOIN', 'INNER', 'LEFT', 'RIGHT', 'FULL', 'OUTER', 'ON', 'USING',
    'GROUP', 'BY', 'HAVING', 'ORDER', 'ASC', 'DESC', 'LIMIT', 'OFFSET',
    'UNION', 'INTERSECT', 'EXCEPT', 'ALL', 'DISTINCT', 'AS', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END',
    'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'SUBSTRING', 'LENGTH', 'UPPER', 'LOWER', 'TRIM'
  ]

  // 注册SQL语言
  monacoInstance.languages.register({ id: 'sql' })

  // 设置语法高亮
  monacoInstance.languages.setMonarchTokensProvider('sql', {
    keywords: sqlKeywords,
    operators: ['=', '>', '<', '!', '~', '?', ':', '==', '<=', '>=', '!=', '&&', '||', '++', '--', '+', '-', '*', '/', '&', '|', '^', '%', '<<', '>>', '>>>', '+=', '-=', '*=', '/=', '&=', '|=', '^=', '%=', '<<=', '>>=', '>>>='],
    symbols: /[=><!~?:&|+\-*\/\^%]+/,
    tokenizer: {
      root: [
        [/[a-z_$][\w$]*/, {
          cases: {
            '@keywords': 'keyword',
            '@default': 'identifier'
          }
        }],
        [/[A-Z_$][\w$]*/, {
          cases: {
            '@keywords': 'keyword',
            '@default': 'type.identifier'
          }
        }],
        [/"([^"\\]|\\.)*$/, 'string.invalid'],
        [/"/, 'string', '@string_double'],
        [/'([^'\\]|\\.)*$/, 'string.invalid'],
        [/'/, 'string', '@string_single'],
        [/\d*\.\d+([eE][\-+]?\d+)?/, 'number.float'],
        [/0[xX][0-9a-fA-F]+/, 'number.hex'],
        [/\d+/, 'number'],
        [/[;,.]/, 'delimiter'],
        [/[()]/, '@brackets'],
        [/@symbols/, {
          cases: {
            '@operators': 'operator',
            '@default': ''
          }
        }],
        [/--.*$/, 'comment'],
        [/\/\*/, 'comment', '@comment'],
      ],
      comment: [
        [/[^\/*]+/, 'comment'],
        [/\*\//, 'comment', '@pop'],
        [/[\/*]/, 'comment']
      ],
      string_double: [
        [/[^\\"]+/, 'string'],
        [/"/, 'string', '@pop']
      ],
      string_single: [
        [/[^\\']+/, 'string'],
        [/'/, 'string', '@pop']
      ]
    }
  })
}

// 更新编辑器大小
const updateEditorSize = () => {
  if (!editor || !editorContainer.value) return

  const container = editorContainer.value
  const height = typeof props.height === 'number' ? `${props.height}px` : props.height
  const width = typeof props.width === 'number' ? `${props.width}px` : props.width

  container.style.height = height
  container.style.width = width

  editor.layout()
}

// 监听props变化
watch(() => getCurrentValue(), (newValue) => {
  if (editor && editor.getValue() !== newValue) {
    editor.setValue(newValue || '')
  }
})

watch(() => props.language, (newLanguage) => {
  if (editor) {
    const model = editor.getModel()
    if (model) {
      monaco.editor.setModelLanguage(model, newLanguage)
    }
  }
})

watch(() => props.theme, (newTheme) => {
  if (editor) {
    monaco.editor.setTheme(newTheme)
  }
})

watch(() => props.readonly, (newReadonly) => {
  if (editor) {
    editor.updateOptions({ readOnly: newReadonly })
  }
})

watch([() => props.height, () => props.width], () => {
  nextTick(() => {
    updateEditorSize()
  })
})

watch(() => props.options, (newOptions) => {
  if (editor) {
    editor.updateOptions(newOptions)
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initEditor()
  })
})

onBeforeUnmount(() => {
  if (editor) {
    editor.dispose()
    editor = null
  }
})

// 暴露方法给父组件
defineExpose({
  getEditor: () => editor,
  getValue: () => editor?.getValue() || '',
  setValue: (value: string) => editor?.setValue(value),
  focus: () => editor?.focus(),
  layout: () => editor?.layout()
})
</script>

<script lang="ts">
export default {
  name: 'MonacoEditor'
}
</script>

<style scoped>
.monaco-editor-container {
  width: 100%;
  height: 300px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.monaco-editor-container:focus-within {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}
</style>