package controllers

import (
	"time"

	"github.com/devops-microservices/sql-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// SchedulerController 任务调度控制器
type SchedulerController struct {
	*BaseController
	schedulerSvc services.SchedulerService
}

// NewSchedulerController 创建任务调度控制器
func NewSchedulerController(schedulerSvc services.SchedulerService, log *logrus.Logger) *SchedulerController {
	return &SchedulerController{
		BaseController: NewBaseController(log),
		schedulerSvc:   schedulerSvc,
	}
}

// AddSqlSchedule 添加SQL定时执行任务
// @Summary 添加SQL定时执行任务
// @Description 为SQL工单添加定时执行任务
// @Tags 任务调度
// @Accept json
// @Produce json
// @Param request body map[string]interface{} true "调度请求"
// @Success 200 {object} models.APIResponse
// @Router /schedule/sql [post]
func (sc *SchedulerController) AddSqlSchedule(c *gin.Context) {
	sc.LogRequest(c, "添加SQL定时执行任务")

	var req map[string]interface{}
	if !sc.ValidateJSON(c, &req) {
		return
	}

	name, ok := req["name"].(string)
	if !ok || name == "" {
		sc.BadRequestResponse(c, "任务名称不能为空")
		return
	}

	runDateStr, ok := req["run_date"].(string)
	if !ok || runDateStr == "" {
		sc.BadRequestResponse(c, "执行时间不能为空")
		return
	}

	runDate, err := time.Parse("2006-01-02 15:04:05", runDateStr)
	if err != nil {
		sc.BadRequestResponse(c, "执行时间格式错误")
		return
	}

	workflowIDFloat, ok := req["workflow_id"].(float64)
	if !ok {
		sc.BadRequestResponse(c, "工单ID不能为空")
		return
	}
	workflowID := uint(workflowIDFloat)

	if err := sc.schedulerSvc.AddSqlSchedule(name, runDate, workflowID); err != nil {
		sc.log.Errorf("添加SQL定时任务失败: %v", err)
		sc.InternalErrorResponse(c, "添加定时任务失败: "+err.Error())
		return
	}

	sc.SuccessResponse(c, map[string]interface{}{
		"message":  "定时任务添加成功",
		"name":     name,
		"run_date": runDate.Format("2006-01-02 15:04:05"),
	})
}

// AddKillConnSchedule 添加终止连接定时任务
// @Summary 添加终止连接定时任务
// @Description 添加终止数据库连接的定时任务
// @Tags 任务调度
// @Accept json
// @Produce json
// @Param request body map[string]interface{} true "调度请求"
// @Success 200 {object} models.APIResponse
// @Router /schedule/kill [post]
func (sc *SchedulerController) AddKillConnSchedule(c *gin.Context) {
	sc.LogRequest(c, "添加终止连接定时任务")

	var req map[string]interface{}
	if !sc.ValidateJSON(c, &req) {
		return
	}

	name, ok := req["name"].(string)
	if !ok || name == "" {
		sc.BadRequestResponse(c, "任务名称不能为空")
		return
	}

	runDateStr, ok := req["run_date"].(string)
	if !ok || runDateStr == "" {
		sc.BadRequestResponse(c, "执行时间不能为空")
		return
	}

	runDate, err := time.Parse("2006-01-02 15:04:05", runDateStr)
	if err != nil {
		sc.BadRequestResponse(c, "执行时间格式错误")
		return
	}

	instanceIDFloat, ok := req["instance_id"].(float64)
	if !ok {
		sc.BadRequestResponse(c, "实例ID不能为空")
		return
	}
	instanceID := uint(instanceIDFloat)

	threadIDFloat, ok := req["thread_id"].(float64)
	if !ok {
		sc.BadRequestResponse(c, "线程ID不能为空")
		return
	}
	threadID := int(threadIDFloat)

	if err := sc.schedulerSvc.AddKillConnSchedule(name, runDate, instanceID, threadID); err != nil {
		sc.log.Errorf("添加终止连接定时任务失败: %v", err)
		sc.InternalErrorResponse(c, "添加定时任务失败: "+err.Error())
		return
	}

	sc.SuccessResponse(c, map[string]interface{}{
		"message":  "定时任务添加成功",
		"name":     name,
		"run_date": runDate.Format("2006-01-02 15:04:05"),
	})
}

// AddSyncUserSchedule 添加同步用户定时任务
// @Summary 添加同步用户定时任务
// @Description 添加同步钉钉用户的定时任务
// @Tags 任务调度
// @Accept json
// @Produce json
// @Success 200 {object} models.APIResponse
// @Router /schedule/sync [post]
func (sc *SchedulerController) AddSyncUserSchedule(c *gin.Context) {
	sc.LogRequest(c, "添加同步用户定时任务")

	if err := sc.schedulerSvc.AddSyncUserSchedule(); err != nil {
		sc.log.Errorf("添加同步用户定时任务失败: %v", err)
		sc.InternalErrorResponse(c, "添加定时任务失败: "+err.Error())
		return
	}

	sc.SuccessResponse(c, map[string]interface{}{
		"message": "同步用户定时任务添加成功",
	})
}

// DeleteSchedule 删除定时任务
// @Summary 删除定时任务
// @Description 根据任务名称删除定时任务
// @Tags 任务调度
// @Accept json
// @Produce json
// @Param name path string true "任务名称"
// @Success 200 {object} models.APIResponse
// @Router /schedule/{name} [delete]
func (sc *SchedulerController) DeleteSchedule(c *gin.Context) {
	sc.LogRequest(c, "删除定时任务")

	name := c.Param("name")
	if name == "" {
		sc.BadRequestResponse(c, "任务名称不能为空")
		return
	}

	if err := sc.schedulerSvc.DeleteSchedule(name); err != nil {
		sc.log.Errorf("删除定时任务失败: %v", err)
		sc.InternalErrorResponse(c, "删除定时任务失败: "+err.Error())
		return
	}

	sc.SuccessResponse(c, map[string]interface{}{
		"message": "定时任务删除成功",
		"name":    name,
	})
}

// GetTaskInfo 获取任务详情
// @Summary 获取任务详情
// @Description 根据任务名称获取定时任务详情
// @Tags 任务调度
// @Accept json
// @Produce json
// @Param name path string true "任务名称"
// @Success 200 {object} models.APIResponse
// @Router /schedule/{name} [get]
func (sc *SchedulerController) GetTaskInfo(c *gin.Context) {
	sc.LogRequest(c, "获取任务详情")

	name := c.Param("name")
	if name == "" {
		sc.BadRequestResponse(c, "任务名称不能为空")
		return
	}

	task, err := sc.schedulerSvc.GetTaskInfo(name)
	if err != nil {
		sc.log.Errorf("获取任务详情失败: %v", err)
		sc.NotFoundResponse(c, "任务不存在")
		return
	}

	sc.SuccessResponse(c, task)
}

// GetAllTasks 获取所有任务
// @Summary 获取所有任务
// @Description 获取所有定时任务列表
// @Tags 任务调度
// @Accept json
// @Produce json
// @Success 200 {object} models.APIResponse
// @Router /schedule/tasks [get]
func (sc *SchedulerController) GetAllTasks(c *gin.Context) {
	sc.LogRequest(c, "获取所有任务")

	// 类型断言获取具体实现
	if impl, ok := sc.schedulerSvc.(*services.SchedulerServiceImpl); ok {
		tasks := impl.GetAllTasks()
		sc.SuccessResponse(c, map[string]interface{}{
			"tasks": tasks,
			"count": len(tasks),
		})
	} else {
		sc.InternalErrorResponse(c, "获取任务列表失败")
	}
}

// GetRunningTasks 获取正在运行的任务
// @Summary 获取正在运行的任务
// @Description 获取当前正在运行的定时任务列表
// @Tags 任务调度
// @Accept json
// @Produce json
// @Success 200 {object} models.APIResponse
// @Router /schedule/running [get]
func (sc *SchedulerController) GetRunningTasks(c *gin.Context) {
	sc.LogRequest(c, "获取正在运行的任务")

	// 类型断言获取具体实现
	if impl, ok := sc.schedulerSvc.(*services.SchedulerServiceImpl); ok {
		runningTasks := impl.GetRunningTasks()
		sc.SuccessResponse(c, map[string]interface{}{
			"tasks": runningTasks,
			"count": len(runningTasks),
		})
	} else {
		sc.InternalErrorResponse(c, "获取运行任务列表失败")
	}
}

// GetTaskStats 获取任务统计
// @Summary 获取任务统计
// @Description 获取定时任务的统计信息
// @Tags 任务调度
// @Accept json
// @Produce json
// @Success 200 {object} models.APIResponse
// @Router /schedule/stats [get]
func (sc *SchedulerController) GetTaskStats(c *gin.Context) {
	sc.LogRequest(c, "获取任务统计")

	// 类型断言获取具体实现
	if impl, ok := sc.schedulerSvc.(*services.SchedulerServiceImpl); ok {
		stats := impl.GetTaskCount()
		result := make(map[string]interface{})
		for k, v := range stats {
			result[k] = v
		}
		result["scheduler_running"] = sc.schedulerSvc.IsRunning()
		sc.SuccessResponse(c, result)
	} else {
		sc.InternalErrorResponse(c, "获取任务统计失败")
	}
}

// StartScheduler 启动调度器
// @Summary 启动调度器
// @Description 启动任务调度器
// @Tags 任务调度
// @Accept json
// @Produce json
// @Success 200 {object} models.APIResponse
// @Router /schedule/start [post]
func (sc *SchedulerController) StartScheduler(c *gin.Context) {
	sc.LogRequest(c, "启动调度器")

	if err := sc.schedulerSvc.Start(); err != nil {
		sc.log.Errorf("启动调度器失败: %v", err)
		sc.InternalErrorResponse(c, "启动调度器失败: "+err.Error())
		return
	}

	sc.SuccessResponse(c, map[string]interface{}{
		"message": "调度器启动成功",
		"status":  "running",
	})
}

// StopScheduler 停止调度器
// @Summary 停止调度器
// @Description 停止任务调度器
// @Tags 任务调度
// @Accept json
// @Produce json
// @Success 200 {object} models.APIResponse
// @Router /schedule/stop [post]
func (sc *SchedulerController) StopScheduler(c *gin.Context) {
	sc.LogRequest(c, "停止调度器")

	if err := sc.schedulerSvc.Stop(); err != nil {
		sc.log.Errorf("停止调度器失败: %v", err)
		sc.InternalErrorResponse(c, "停止调度器失败: "+err.Error())
		return
	}

	sc.SuccessResponse(c, map[string]interface{}{
		"message": "调度器停止成功",
		"status":  "stopped",
	})
}
