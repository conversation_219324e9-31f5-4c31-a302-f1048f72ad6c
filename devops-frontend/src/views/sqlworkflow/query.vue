<template>
  <div class="query-menu">
    <!-- <el-container
      style="border: 1px solid #e6e6e6;margin-top: 15px;box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);"
    > -->
    <el-container>
      <transition name="el-zoom-in-center">
        <el-aside
          v-show="showLeftSide"
          :style="{
            position: 'fixed',
            'background-color': '#ffffff',
            padding: 0,
            'padding-top': '20px',
            'border-right': 'solid 1px #e6e6e6 !important',
            width: withoutLeftSideWidth
          }"
          v-loading="instanceLoading"
          :width="withoutLeftSideWidth"
        >
          <!-- <div style="height: 100%;"> -->
          <!-- <el-scrollbar
            ref="sqlmenu"
            style="height: 100%;"
            wrap-style="overflow-x: hidden;"
          > -->
          <!-- <el-input
              v-model="query"
              type="text"
              class="leftsearch"
              placeholder="搜索数据库、表"
              @keyup.enter="searchParam"
              @change="searchParam"
            >
              <i
                slot="prefix"
                class="el-input__icon el-icon-search"
                style="top: -5px; position: absolute"
              ></i>
              <template slot="append">
                <el-button
                  type="text"
                  size="medium"
                  class="searchbtn"
                  icon="el-icon-caret-left"
                  @click="searchPrev"
                ></el-button>
                <el-button
                  type="text"
                  size="medium"
                  class="searchbtn"
                  icon="el-icon-caret-right"
                  @click="searchNext"
                ></el-button>
              </template>
            </el-input> -->
          <el-input
            v-model="filterText"
            type="text"
            class="leftsearch"
            placeholder="搜索数据库、表"
          ></el-input>
          <div class="dbmenu" style="margin-top: 38px">
            <span style="margin-left: 14px; color: #606266">
              <i class="el-icon-d-arrow-right"></i> 数据库实例
            </span>
            <span style="float: right; font-size: 16px; margin-right: 18px">
              <el-tooltip class="item" effect="dark" content="申请实例" placement="top">
                <el-button
                  type="text"
                  size="medium"
                  @click="jumpWorkflow"
                  icon="el-icon-plus"
                  style=""
                ></el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="Refresh" placement="top">
                <el-button
                  type="text"
                  size="medium"
                  icon="el-icon-refresh"
                  @click="getInstances"
                  style=""
                ></el-button>
              </el-tooltip>
            </span>
          </div>
          <el-empty
            v-if="!instances.length && !instanceLoading"
            description="暂无可用实例，请到工单管理申请"
            :image-size="70"
          >
            <el-button type="primary" size="small" @click="jumpWorkflow"
              ><i class="el-icon-plus"></i>申请实例</el-button
            >
          </el-empty>
          <el-divider />
          <!-- <el-tree
  :props="props"
  :load="loadNode"
  lazy
  show-checkbox>
</el-tree> -->
          <div class="ve-tree" style="height: calc(100vh - 0px)">
            <!-- Just remove the height parameter when not using virtual scrolling -->
            <vue-easy-tree
              ref="veTree"
              node-key="instance_id"
              height="calc(100vh - 205px)"
              :highlight-current="true"
              :load="loadNode"
              lazy
              :props="props"
              :filter-node-method="filterNode"
              @node-click="handleNodeClick"
            >
              <span slot-scope="{ node, data }">
                <i :class="nodeClassMap(node.level)" style="margin-right: 5px; font-size: 18px"></i>
                <span style="font-size: 18px">{{ node.label }}</span>
              </span>
            </vue-easy-tree>
          </div>
          <!-- </el-scrollbar> -->
          <!-- </div> -->
        </el-aside>
      </transition>
      <el-container>
        <el-header
          :style="{
            'font-size': '12px',
            'margin-top': '20px',
            height: '35px',
            'margin-left': withoutLeftSideWidth
          }"
        >
          <!-- <span
            style="color: #606266;font-size: 18px;margin: 15px 0;margin-right: 15px;display: inline-block;"
            >SQL在线查询</span
          > -->
          <el-tooltip :content="showLeftSide ? '折叠实例' : '展开实例'" placement="top">
            <el-button
              type="text"
              @click="leftSideHandle"
              :icon="showLeftSide ? 'el-icon-s-fold' : 'el-icon-s-unfold'"
              :style="{ left: menuLeft, position: 'fixed', color: '#606266', 'font-size': '18px' }"
            >
            </el-button>
          </el-tooltip>
          <el-row style="margin-bottom: 15px">
            <el-col :span="24">
              <!-- <span style="color: #606266; margin: 15px 0;">选择数据库</span> -->
              <el-button
                type="primary"
                size="mini"
                class="is-text is-has-bg"
                icon="el-icon-plus"
                @click="debounceEdit"
                >SQL查询</el-button
              >
              <el-select
                v-model="form.instance"
                filterable
                placeholder="请选择数据库实例"
                size="mini"
                style="margin-left: 15px"
                value-key="instance_id"
                :disabled="form.disabled"
                @change="setInsMenu"
              >
                <el-option
                  v-for="(item, index) in instances"
                  :key="'dbins-' + index"
                  :label="item.instance_name"
                  :value="item"
                />
              </el-select>
              <el-select
                v-if="form.instance"
                v-model="form.db_name"
                filterable
                placeholder="请选择要操作的数据库"
                size="mini"
                style="margin-left: 15px"
                :disabled="form.disabled"
                @change="setDBMenu"
              >
                <el-option
                  v-for="(item, index) in form.instance.databases"
                  :key="'db-' + index"
                  :label="item"
                  :value="item"
                />
              </el-select>
              <el-select
                v-if="form.instance && form.instance.db_type === 'pgsql' && form.db_name"
                v-model="form.schema_name"
                filterable
                placeholder="请选择要操作的模式"
                size="mini"
                style="margin-left: 15px"
                :disabled="form.disabled"
                @change="setSchemaMenu"
              >
                <el-option
                  v-for="(item, index) in dbSchemas[
                    `${form.instance.instance_id}##${form.db_name}`
                  ]"
                  :key="'schema-' + index"
                  :label="item"
                  :value="item"
                />
              </el-select>
              <el-select
                v-model="form.limit_num"
                size="mini"
                style="margin-left: 15px"
                :disabled="form.disabled"
                @change="setLimit"
              >
                <el-option label="100" :value="100"></el-option>
                <el-option label="500" :value="500"></el-option>
                <el-option label="1000" :value="1000"></el-option>
                <el-option label="5000" :value="5000"></el-option>
                <el-option label="10000" :value="10000"></el-option>
              </el-select>
              <div style="margin-left: 8px; color: #606266; margin-top: 10px; margin-bottom: 10px">
                <em>1. 默认查询100条数据. 2. 默认左侧菜单点击数据表后才会加列名加入Edit器提示.</em>
              </div>
            </el-col>
          </el-row>
        </el-header>
        <!-- <el-divider></el-divider> -->
        <el-main
          :style="{ 'padding-top': '20px', 'margin-left': withoutLeftSideWidth }"
          v-loading="downLoading"
        >
          <el-divider></el-divider>
          <el-tooltip :content="showEditor ? '折叠Edit框' : '展开Edit框'" placement="top">
            <el-button
              type="text"
              @click="editorVisibleHandle"
              :icon="showEditor ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"
              :style="{
                top: '180px',
                left: withoutLeftSideWidth,
                position: 'fixed',
                color: '#606266',
                'font-size': '20px'
              }"
            >
            </el-button>
          </el-tooltip>
          <el-row>
            <el-col :span="24">
              <!-- editable
                @edit="debounceEdit" -->
              <el-tabs
                v-model="queryTabsValue"
                type="card"
                closable
                addable
                @tab-remove="removeQueryTab"
                @tab-add="debounceEdit"
              >
                <el-tab-pane
                  v-for="(item, index) in queryTabs"
                  :key="'query-' + item.name"
                  :label="`${item.title}`"
                  :name="item.name"
                >
                  <template slot="label"
                    ><el-tooltip :content="item.title" placement="top"
                      ><span
                        ><el-button
                          v-if="queryTabsValue === item.name && item.meta"
                          type="text"
                          icon="el-icon-refresh"
                          @click="
                            openTableWithData(
                              item.instance_id,
                              item.db_name,
                              item.schema_name,
                              tableDescData[item.tbdata].tb_name,
                              dbColumns[item.tbdata]
                            )
                          "
                          style="font-size: 12px; margin-right: 5px"
                        ></el-button
                        >{{ item.title.split('@')[0] }}</span
                      ></el-tooltip
                    ></template
                  >
                  <template v-if="item.meta">
                    <el-tabs tab-position="left" style="min-height: 200px">
                      <el-tab-pane label="数据">
                        <template slot="label"> 数据 </template>
                        <!-- <el-card
                          class="box-card"
                          shadow="never"
                          style="margin-bottom: 15px;"
                        > -->
                        <!-- <span style="color: #606266;"
                          ><i class="el-icon-d-arrow-right"></i>&nbsp;
                          展示近20条数据<em
                            style="margin-left: 15px;font-size: 14px;"
                            >如需更多数据，请使用查询功能.</em
                          ></span
                        > -->
                        <!-- <el-divider></el-divider> -->
                        <el-table
                          :ref="`tableref-${index}`"
                          v-if="tableDescData[item.tbdata]"
                          v-loading="listLoading"
                          :data="tableDescData[item.tbdata].data"
                          :row-class-name="tableRowClassName"
                          element-loading-text="加载中"
                          style="width: 100%"
                          border
                          @cell-dblclick="
                            (row, column, cell, event) =>
                              showCellContent(
                                row,
                                column,
                                cell,
                                event,
                                tableDescData[item.tbdata].columns
                              )
                          "
                          @sort-change="
                            (column, prop, order) =>
                              sortChange(
                                column,
                                prop,
                                order,
                                item.instance_id,
                                item.db_name,
                                item.schema_name,
                                tableDescData[item.tbdata].tb_name,
                                dbColumns[item.tbdata]
                              )
                          "
                        >
                          <el-table-column
                            v-for="(sitem, dataIndex) in tableDescData[item.tbdata].columns"
                            :key="'col-' + index + '-' + dataIndex"
                            :label="sitem"
                            :prop="sitem"
                            sortable="custom"
                            :show-overflow-tooltip="true"
                            :min-width="
                              flexColumnWidth(
                                tableDescData[item.tbdata].data,
                                dataIndex,
                                sitem,
                                sitem
                              )
                            "
                          >
                            <!-- <template slot="header" slot-scope="scope">
                            {{ sitem }}@@
                        </template> -->
                            <template slot-scope="{ row }">
                              <!-- <el-tooltip placement="top">
                                <div slot="content">
                                  {{ row[dataIndex] }}
                                </div>
                                <span>{{
                                  row[dataIndex] | cellFilter
                                }}</span>
                              </el-tooltip> -->
                              <!-- <div>
                                <el-popover
                                  placement="top-start"
                                  :title="sitem"
                                  width="900"
                                  trigger="hover"
                                  :content="'' + row[dataIndex]"
                                >
                                  <span slot="reference">{{
                                    row[dataIndex] | cellFilter
                                  }}</span>
                                </el-popover>
                              </div> -->
                              <!-- <div v-else>
                                {{ row[dataIndex] }}
                              </div> -->
                              <span
                                v-if="
                                  !row[dataIndex] ||
                                  (row[dataIndex] && typeof row[dataIndex] === 'number')
                                "
                              >
                                {{ row[dataIndex] }}
                              </span>
                              <span v-else>
                                {{ row[dataIndex].substring(0, 3000) }}
                              </span>
                            </template>
                          </el-table-column>
                        </el-table>
                        <div
                          style="
                            font-size: 12px;
                            color: #f00;
                            padding-top: 18px;
                            padding-left: 20px;
                          "
                        >
                          <em>注意：总数可能不是最新的统计</em>
                        </div>
                        <pagination
                          v-if="item.data && item.data.meta_data && item.data.meta_data.rows"
                          :page-sizes="[20, 50, 100]"
                          :page.sync="tableDescData[item.tbdata].current_page"
                          :limit.sync="tableDescData[item.tbdata].page_size"
                          :total="item.data.meta_data.rows[3]"
                          @pagination="
                            openTableWithData(
                              item.instance_id,
                              item.db_name,
                              item.schema_name,
                              tableDescData[item.tbdata].tb_name,
                              dbColumns[item.tbdata]
                            )
                          "
                        />
                        <!-- </el-card> -->
                      </el-tab-pane>
                      <el-tab-pane label="表信息[metaData]"
                        ><el-card
                          v-if="item.data && item.data.meta_data"
                          class="box-card"
                          shadow="never"
                          style="margin-bottom: 15px"
                        >
                          <div slot="header" class="clearfix">
                            <span style="color: #606266"
                              ><i class="el-icon-d-arrow-right"></i>&nbsp;表信息[metaData]</span
                            >
                          </div>
                          <el-descriptions class="margin-top" title="" :column="5" border>
                            <el-descriptions-item
                              v-for="(obj, idx) in item.data.meta_data.column_list"
                              :key="'tbmeta-' + obj"
                            >
                              <template slot="label">
                                {{ tableObj[obj] }}
                              </template>
                              {{ item.data.meta_data.rows[idx] }}
                            </el-descriptions-item>
                          </el-descriptions>
                        </el-card></el-tab-pane
                      >
                      <el-tab-pane
                        v-for="(metaLabel, metaItem) in metaObj"
                        :key="'adv-' + metaItem"
                        :label="`${metaLabel}[${metaItem}]`"
                      >
                        <el-card
                          v-if="item.data && item.data[metaItem]"
                          class="box-card"
                          shadow="never"
                          style="margin-bottom: 15px"
                        >
                          <div slot="header" class="clearfix">
                            <span style="color: #606266"
                              ><i class="el-icon-d-arrow-right"></i>&nbsp;{{ metaLabel }}[{{
                                metaItem
                              }}]</span
                            >
                          </div>
                          <el-table
                            v-if="
                              item.data &&
                              item.data[metaItem] &&
                              item.data[metaItem].rows &&
                              item.data[metaItem].rows.length
                            "
                            v-loading="listLoading"
                            :data="item.data[metaItem].rows"
                            :row-class-name="tableRowClassName"
                            element-loading-text="加载中"
                            width="100%"
                            highlight-current-row
                            :style="{ 'margin-top': '20px' }"
                          >
                            <el-table-column
                              :show-overflow-tooltip="true"
                              v-for="(mitem, index) in item.data[metaItem].column_list"
                              :key="'col-' + index"
                              :label="mitem"
                              :width="mitem === 'id' ? '80' : 'auto'"
                              style="white-space: pre-line"
                            >
                              <template slot-scope="{ row }">
                                <span>
                                  {{ row[index] }}
                                </span>
                              </template>
                            </el-table-column>
                          </el-table>
                          <el-empty v-else :image-size="20"></el-empty>
                        </el-card>
                      </el-tab-pane>
                      <el-tab-pane label="建表语句[create]"
                        ><el-card
                          v-if="item.data && item.data.meta_data"
                          class="box-card"
                          shadow="never"
                          style="margin-bottom: 15px"
                        >
                          <div slot="header" class="clearfix">
                            <span style="color: #606266"
                              ><i class="el-icon-d-arrow-right"></i>&nbsp;建表语句[create]</span
                            >
                          </div>
                          <MAceEditor
                            v-if="item.data && item.data.create_sql"
                            :ref="`sqlcreate-${index}`"
                            v-model="item.data.create_sql[0][1]"
                            :autoresize="true"
                            :min-line="15"
                            :max-line="30"
                            :line-number="true"
                            :read-only="true"
                            :line-wrap="true"
                            :fold-depth="1"
                            line-height="16px"
                            mode="mysql"
                            theme="iplastic"
                          /> </el-card
                      ></el-tab-pane>
                    </el-tabs>
                  </template>
                  <div v-else>
                    <transition name="el-zoom-in-top">
                      <el-form
                        v-show="showEditor"
                        :ref="`sqlForm-${index}`"
                        label-position="right"
                        label-width="0"
                        :model="item"
                        :disabled="disabled"
                      >
                        <el-form-item prop="sql_content">
                          <MAceEditor
                            :ref="`sqleditor-${item.name}`"
                            v-model="item.sql_content"
                            :autoresize="true"
                            :min-line="22"
                            :max-line="50"
                            :line-number="true"
                            :read-only="disabled"
                            :line-wrap="true"
                            :fold-depth="1"
                            line-height="16px"
                            :status-loading="true"
                            mode="mysql"
                            theme="iplastic"
                          />
                        </el-form-item>
                        <el-form-item>
                          <div v-if="item.errMsg && item.errMsg.msg" class="errmsg">
                            {{ item.errMsg.msg }}
                          </div>
                          <el-button
                            type="primary"
                            size="medium"
                            class="is-text is-has-bg"
                            @click="sqlFormat(item)"
                            >SQL美化</el-button
                          >
                          <el-button
                            type="info"
                            size="medium"
                            class="is-text is-has-bg"
                            @click="debounceQuery(item, 'sqlExplain')"
                            >执行计划</el-button
                          >
                          <el-button
                            type="warning"
                            size="medium"
                            class="is-text is-has-bg"
                            @click="openAdvisorDialog(item)"
                            >获取优化建议</el-button
                          >
                          <el-button
                            type="success"
                            size="medium"
                            class="is-text is-has-bg"
                            @click="debounceQuery(item, 'sqlQuery')"
                            >SQL查询</el-button
                          >
                        </el-form-item>
                        <el-form-item></el-form-item>
                      </el-form>
                    </transition>
                  </div>
                  <div v-if="item.data" style="color: #606266">
                    <em v-if="item.data.query_time" style="font-size: 14px"
                      >查询时间：{{ item.data.query_time }}</em
                    >
                    <em v-if="item.data.mask_rule_hit" style="margin-left: 15px; font-size: 14px"
                      >脱敏时间：{{ item.data.mask_time }}</em
                    >
                    <!-- <el-button type="primary" size="small" @click="downloadData('/api/sql/instance/download/', 'SQL查询结果', 'sql')">下载</el-button> -->
                    <el-dropdown
                      v-if="item.data && item.data.query_time"
                      size="small"
                      style="float: right; margin-bottom: 10px"
                      @command="
                        cmd =>
                          downloadData(
                            '/api/sql/instance/download/',
                            `${item.title}查询结果`,
                            cmd,
                            item
                          )
                      "
                    >
                      <el-button type="primary" size="small">
                        <i class="el-icon-download"></i> 导出
                        <i class="el-icon-arrow-down el-icon--right"></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="xlsx">XLSX</el-dropdown-item>
                        <el-dropdown-item command="csv">CSV</el-dropdown-item>
                        <el-dropdown-item command="sql">SQL</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                  <!-- 执行计划结果、SQL查询结果展示 -->
                  <el-table
                    v-if="item.columns && item.columns.length"
                    v-loading="listLoading"
                    :data="item.tableData"
                    :row-class-name="tableRowClassName"
                    element-loading-text="加载中"
                    border
                    width="100%"
                    highlight-current-row
                    :style="{ 'margin-top': '20px' }"
                    @cell-dblclick="
                      (row, column, cell, event) =>
                        showCellContent(row, column, cell, event, item.columns)
                    "
                  >
                    <el-table-column type="expand">
                      <template slot-scope="props">
                        <el-form label-position="left" inline class="demo-table-expand">
                          <el-form-item
                            v-for="(item, index) in item.columns"
                            :key="'col-' + index"
                            :label="item"
                          >
                            :
                            <span style="padding-left: 10px">{{ props.row[index] }}</span>
                          </el-form-item>
                        </el-form>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-for="(sitem, dataIndex) in item.columns"
                      :key="'col-' + dataIndex"
                      :label="sitem"
                      :auto-fit="true"
                      :prop="sitem"
                      sortable
                      :show-overflow-tooltip="true"
                      :min-width="flexColumnWidth(item.tableData, dataIndex, sitem, sitem)"
                    >
                      <!-- <template slot="header">
                        {{ item }}
                      </template>-->
                      <template slot-scope="{ row }">
                        <!-- <div>
                          <el-popover
                            placement="top-start"
                            :title="sitem"
                            width="800"
                            trigger="hover"
                            :content="'' + row[dataIndex]"
                          >
                            <span slot="reference">{{
                              row[dataIndex] | cellFilter
                            }}</span>
                          </el-popover>
                        </div> -->
                        <!-- <div v-else>
                          {{ row[dataIndex] | cellFilter }}
                        </div> -->
                        <span
                          v-if="
                            !row[dataIndex] ||
                            (row[dataIndex] && typeof row[dataIndex] === 'number')
                          "
                        >
                          {{ row[dataIndex] }}
                        </span>
                        <span v-else>
                          {{ row[dataIndex].substring(0, 3000) }}
                        </span>
                      </template>
                    </el-table-column>
                  </el-table>
                  <pagination
                    v-if="item.total"
                    :total="item.total"
                    :page-sizes="[10, 20, 50, 100]"
                    :page.sync="item.current_page"
                    :limit.sync="item.page_size"
                    @pagination="getData(item)"
                  />

                  <!-- soar优化建议 -->
                  <MAceEditor
                    v-if="form.tool === 3 && item.soarData"
                    :ref="'soaradv-' + index"
                    v-model="item.soarData"
                    :autoresize="true"
                    :min-line="15"
                    :max-line="60"
                    :line-number="true"
                    :read-only="true"
                    :disabled="true"
                    :line-wrap="true"
                    :fold-depth="1"
                    line-height="16px"
                    :status-loading="true"
                    mode="mysql"
                    theme="iplastic"
                  />
                  <!-- 显示系统参数 -->
                  <div v-for="(advLabel, advItem) in advisorObj" :key="'adv-' + advItem">
                    <el-card
                      v-if="item.data && item.data[advItem]"
                      class="box-card"
                      shadow="never"
                      style="margin-bottom: 15px"
                    >
                      <div slot="header" class="clearfix">
                        <span style="color: #606266"
                          ><i class="el-icon-d-arrow-right"></i>&nbsp;{{ advLabel }}[{{
                            advItem
                          }}]</span
                        >
                      </div>
                      <el-table
                        v-if="item.data && item.data[advItem] && item.data[advItem].rows.length"
                        v-loading="listLoading"
                        :data="item.data[advItem].rows"
                        :row-class-name="tableRowClassName"
                        element-loading-text="加载中"
                        width="100%"
                        highlight-current-row
                        :style="{ 'margin-top': '20px' }"
                      >
                        <el-table-column type="expand">
                          <template slot-scope="props">
                            <el-form label-position="left" inline class="demo-table-expand">
                              <el-form-item
                                v-for="(mitem, index) in item.data[advItem].column_list"
                                :key="'col-' + index"
                                :label="mitem"
                              >
                                :
                                <span v-if="advItem === 'optimizer_switch'">
                                  <span
                                    v-for="(v, i) in props.row[index].split(',')"
                                    :key="'var-' + i"
                                  >
                                    {{ v }}<br />
                                  </span>
                                </span>
                                <span v-else style="padding-left: 10px">{{
                                  props.row[index]
                                }}</span>
                              </el-form-item>
                            </el-form>
                          </template>
                        </el-table-column>
                        <el-table-column
                          :show-overflow-tooltip="true"
                          v-for="(mitem, index) in item.data[advItem].column_list"
                          :key="'col-' + index"
                          :label="mitem"
                          :width="mitem === 'id' ? '80' : 'auto'"
                          style="white-space: pre-line"
                        >
                          <!-- <template slot="header">
                  {{ item }}
                      </template>-->
                          <template slot-scope="{ row }">
                            <span v-if="advItem === 'optimizer_switch'">
                              <span v-for="(v, i) in row[index].split(',')" :key="'var-' + i">
                                {{ v }}<br />
                              </span>
                            </span>
                            <span v-else>
                              {{ row[index] }}
                            </span>
                            <div v-if="advItem === 'basic_information'">
                              author：MySQL SQL Tuning Tools v1.0 (by hanfeng)
                              <a
                                href="https://dbaplus.cn/blog-77-736-1.html"
                                target="_blank"
                                style="color: #409eff"
                                >查看文章</a
                              >
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                      <el-empty v-else :image-size="20"></el-empty>
                    </el-card>
                  </div>
                  <!-- 显示相关对象(表、索引)统计信息 -->
                  <template v-if="item.data && item.data.object_statistics">
                    <div v-for="(obj, objIdx) in item.data.object_statistics" :key="'ob-' + objIdx">
                      <div v-for="(statLabel, statItem) in statObjs" :key="'stat-' + statItem">
                        <el-card
                          v-if="obj[statItem]"
                          class="box-card"
                          shadow="never"
                          style="margin-bottom: 15px"
                        >
                          <div slot="header" class="clearfix">
                            <span style="color: #606266"
                              ><i class="el-icon-d-arrow-right"></i>&nbsp; {{ statLabel }}[{{
                                statItem
                              }}]</span
                            >
                          </div>
                          <el-table
                            v-if="obj[statItem].rows.length"
                            v-loading="listLoading"
                            :data="obj[statItem].rows"
                            :row-class-name="tableRowClassName"
                            element-loading-text="加载中"
                            width="100%"
                            highlight-current-row
                            :style="{ 'margin-top': '20px' }"
                          >
                            <el-table-column type="expand">
                              <template slot-scope="props">
                                <el-form label-position="left" inline class="demo-table-expand">
                                  <el-form-item
                                    v-for="(mitem, index) in obj[statItem].column_list"
                                    :key="'col-' + index"
                                    :label="mitem"
                                  >
                                    :
                                    <span style="padding-left: 10px">{{ props.row[index] }}</span>
                                  </el-form-item>
                                </el-form>
                              </template>
                            </el-table-column>
                            <el-table-column
                              :show-overflow-tooltip="true"
                              v-for="(mitem, index) in obj[statItem].column_list"
                              :key="'col-' + index"
                              :label="mitem"
                              :width="mitem === 'id' ? '80' : 'auto'"
                              style="white-space: pre-line"
                            >
                              <template slot-scope="{ row }">
                                <span>
                                  {{ row[index] }}
                                </span>
                              </template>
                            </el-table-column>
                          </el-table>
                          <el-empty v-else :image-size="20"></el-empty>
                        </el-card>
                      </div>
                    </div>
                  </template>
                </el-tab-pane>
              </el-tabs>
            </el-col>
          </el-row>
          <!-- </el-card> -->
        </el-main>
      </el-container>
    </el-container>

    <el-dialog title="SQL优化建议" :visible.sync="showAdvisorDialog" width="30%">
      <el-row>
        <el-col :span="24" style="margin-bottom: 10px">
          <span style="color: #606266; margin: 15px 0">选择优化工具</span>
          <el-select
            v-model="form.tool"
            size="mini"
            placeholder="请选择优化工具"
            style="margin-left: 15px"
          >
            <el-option
              v-for="(tool, tidx) in advisorTools"
              :key="'tool-' + tidx"
              :label="tool.label"
              :value="tool.value"
            />
          </el-select>
        </el-col>
        <div v-if="form.tool === 2">
          <el-col :span="24" style="margin-bottom: 10px">
            显示系统参数
            <el-switch v-model="form.option.sys_param" style="margin-left: 15px"> </el-switch
          ></el-col>
          <el-col :span="24" style="margin-bottom: 10px">
            显示执行计划
            <el-switch v-model="form.option.sql_plan" :disabled="true" style="margin-left: 15px">
            </el-switch
          ></el-col>
          <el-col :span="24" style="margin-bottom: 10px">
            显示统计信息
            <el-switch v-model="form.option.obj_stat" style="margin-left: 15px"> </el-switch>
            <em style="margin-left: 10px">显示相关对象(表、索引)统计信息</em>
          </el-col>
        </div>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showAdvisorDialog = false">取 消</el-button>
        <el-button type="primary" @click="debounceQuery(currentAdvisorItem, form.tool)"
          >提 交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import MAceEditor from '@/components/mAceEditor/ace-editor.vue'
import cardHeader from '@/views/deploy/module/card-header.vue'
import Pagination from '@/components/Pagination'
import { format } from 'sql-formatter'
import { getToken } from '@/utils/auth'
import Search from '@/module/Search'
import VueEasyTree from '@wchbrad/vue-easy-tree'

export default {
  displayName: 'SQL查询',
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: { Pagination, MAceEditor, cardHeader, Search, VueEasyTree },
  filters: {
    statFilter(val) {
      const statMap = {
        0: 'pass',
        1: 'warning',
        2: 'error'
      }
      return statMap[val]
    },
    cellFilter(val) {
      //   console.log('当前值 ======', val, typeof val)
      //   if (!val || typeof val === 'number') return val
      //   console.log('截取字符长度', val.substring(0, 125))
      if (val && typeof val !== 'number' && val.length > 80) return val.substring(0, 76) + '...' // + ' 点击查看'
      return val
    }
  },
  data() {
    return {
      showEditor: true,
      showLeftSide: true,
      menuLeft: '350px',
      withoutLeftSideWidth: '350px',
      props: {
        label: 'instance_name',
        children: 'children',
        isLeaf: 'leaf'
      },
      sortParam: { prop: 'id', order: 'descending' },
      openIndex: 0,
      matchList: [],
      matchCount: 0,
      searchIndex: 0, //字段所在数组的索引
      filterText: '',
      query: '',
      advisorTools: [
        { label: 'SOAR', value: 3 },
        { label: 'SQLTunning', value: 2 }
      ],
      showAdvisorDialog: false,
      currentAdvisorItem: {},
      tableDescData: {},
      tableObj: {
        table_name: '表名',
        engine: '引擎',
        row_format: '行格式',
        table_rows: '表行数',
        avg_row_length: '平均行长度',
        data_length: '数据长度（K）',
        max_data_length: '最大行长度',
        index_length: '索引长度（K）',
        data_total: '数据总大小（K）',
        data_free: '碎片大小',
        auto_increment: '当前自增值',
        table_collation: '排序规则',
        create_time: '创建时间',
        check_time: '检查时间',
        update_time: '更新时间',
        table_comment: '描述'
      },
      metaObj: {
        desc: '列信息',
        index: '索引信息'
      },
      advisorObj: {
        basic_information: '基础信息',
        optimizer_rewrite_sql: '优化器改写SQL',
        sys_parameter: '系统参数',
        plan: '执行计划',
        optimizer_switch: '优化器开关'
      },
      statObjs: {
        index_info: '索引信息',
        structure: '表结构',
        table_info: '表信息'
      },
      defaultActive: '0',
      defaultOpeneds: [],
      total: 0,
      current_page: 1,
      page_size: 10,
      editableTabsValue: '0',
      editableTabs: [],
      queryTabsValue: 'qs-0',
      queryTabs: [
        {
          name: 'qs-0',
          title: 'SQL',
          sql_content: '',
          total: 0,
          current_page: 1,
          page_size: 10
        }
      ],
      instanceLoading: true,
      listLoading: false,
      completerIndex: { tb: {}, col: {} },
      templateSelect: {},
      form: {
        sql_content: '',
        limit_num: 100,
        tool: 3,
        option: { sql_plan: true, obj_stat: true }
      },
      instances: [],
      tables: {},
      dbColumns: {},
      dbColumnsExtra: {},
      dbSchemas: {},
      limit_num: 100,
      errMsg: {},
      columns: [],
      data: [],
      tableData: [],
      log_id: null,
      downLoading: false,
      descLoading: false,
      tabStep: 1000
    }
  },
  computed: {},
  watch: {
    filterText(val) {
      this.$refs.veTree.filter(val)
    },
    queryTabsValue(val) {
      console.log('queryTabsValue change queryTabsValue', val)
      const ctab = this.queryTabs.filter(item => item.name === this.queryTabsValue)
      if (!ctab[0]) return
      this.$set(this.form, 'disabled', ctab[0].formDisabled || false)
      const instance = this.instances.filter(item => item.instance_id === ctab[0].instance_id)
      if (instance[0]) {
        this.$set(this.form, 'instance', instance[0])
      }
      this.$set(this.form, 'db_name', ctab[0].db_name)
      this.$set(this.form, 'limit_num', ctab[0].limit_num || 100)
    }
  },
  mounted() {
    // this.getInstances()
  },
  created() {},
  methods: {
    leftSideHandle() {
      this.showLeftSide = !this.showLeftSide
      this.withoutLeftSideWidth = this.showLeftSide ? '350px' : '6px'
      this.menuLeft = this.showLeftSide ? '350px' : '6px'
    },
    editorVisibleHandle() {
      this.showEditor = !this.showEditor
    },
    nodeClassMap(level) {
      const _css = {
        1: 'el-icon-connection',
        2: 'el-icon-coin',
        3: 'el-icon-document',
        4: 'el-icon-news',
        5: 'el-icon-refrigerator'
      }
      return _css[level]
    },
    handleNodeClick(data, node, el) {
      console.log('data node click', data)
      console.log('node click node node ', node)
      if (node.level === 1) {
        this.$set(this.form, 'instance', data)
        this.$set(this.form, 'db_name', '')
      }
      if (node.level === 2) {
        this.$set(this.form, 'db_name', data.instance_name)
        this.queryTabHandle(data.instance_name, this.form.instance.instance_id)
        if (this.form.instance.db_type === 'pgsql') {
          this.$set(this.form, 'schema_name', '')
        }
      }
      if (node.level === 3 && this.form.instance.db_type === 'pgsql') {
        this.$set(this.form, 'schema_name', data.instance_name)
        this.queryTabHandle(data.instance_name, this.form.instance.instance_id)
      }
      if (node.level !== 3 && node.level !== 4) return
      let instance_id = ''
      let db_name = ''
      let schema_name = ''
      let tb_name = ''
      if (node.level === 3) {
        instance_id = node.parent.parent.data.instance_id
        db_name = node.parent.data.instance_name
        tb_name = data.instance_name
      } else {
        instance_id = node.parent.parent.parent.data.instance_id
        db_name = node.parent.parent.data.instance_name
        schema_name = node.parent.data.instance_name
        tb_name = data.instance_name
      }
      let ins_name = schema_name
        ? `${schema_name}@${db_name}@${instance_id}`
        : `${db_name}@${instance_id}`
      const instance = this.instances.filter(item => item.instance_id === +instance_id)
      if (instance[0]) {
        ins_name = schema_name
          ? `${schema_name}@${db_name}@${instance[0].instance_name}`
          : `${db_name}@${instance[0].instance_name}`
      }
      const tbArr = this.queryTabs.filter(item => item.title === `${tb_name}@${ins_name}`)
      let tbActive = 'qs-0'
      const dbkey = schema_name
        ? `${tb_name}@${schema_name}@${db_name}@${instance_id}`
        : `${tb_name}@${db_name}@${instance_id}`
      if (!tbArr[0] && this.dbColumns[dbkey]) {
        // 存在列值但标签页未打开，需要重新打开标签页
        tbActive = `qs-${+this.queryTabs[this.queryTabs.length - 1].name.replace('qs-', '') + 1}`
        this.queryTabs.push({
          instance_id: instance_id,
          instance_name: ins_name,
          db_name: db_name,
          schema_name,
          title: `${tb_name}@${ins_name}`,
          name: tbActive,
          data: this.dbColumnsExtra[dbkey],
          tbdata: dbkey,
          meta: true
        })
        this.queryTabsValue = tbActive
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.instance_name.toLowerCase().indexOf(value.toLowerCase()) !== -1
    },
    getDbSchemas(instance_id, db_name, resolve) {
      this.$http
        .getInstanceResource({
          id: instance_id,
          db_name: db_name,
          resource_type: 'schema',
          from_workbench_query: true
        })
        .then(res => {
          const schema = []
          this.$set(this.dbSchemas, `${instance_id}##${db_name}`, res.data.items)
          res.data.items.forEach(item => {
            schema.push({ instance_name: item, instance_id: `${instance_id}##${db_name}##${item}` })
          })
          if (resolve) return resolve(schema)
        })
    },
    getDbTables(instance_id, db_name, schema_name, resolve) {
      this.$http
        .getInstanceResource({
          id: instance_id,
          db_name: db_name,
          schema_name,
          resource_type: 'table',
          from_workbench_query: true
        })
        .then(res => {
          this.$set(this.tables, `${instance_id}##${db_name}`, res.data.items)
          this.form.tb_name = ''
          const insArr = this.instances.filter(item => item.instance_id === instance_id)
          if (insArr.length) this.form.instance = insArr[0]
          this.form.db_name = db_name
          // this.queryTabHandle(db_name, instance_id)
          this.query = ''
          // this.editableTabs = []
          this.addCompleter(this.tables[`${instance_id}##${db_name}`], 'tb')
          let tbs = []
          res.data.items.forEach(item => {
            tbs.push({
              instance_name: item,
              instance_id: `${instance_id}##${db_name}##${schema_name}##${item}`
            })
          })
          if (resolve) return resolve(tbs)
        })
    },
    async getTbColumns(instance_id, db_name, schema_name, tb_name, resolve) {
      let ins_name = schema_name
        ? `${schema_name}@${db_name}@${instance_id}`
        : `${db_name}@${instance_id}`
      const instance = this.instances.filter(item => item.instance_id === +instance_id)
      if (instance[0]) {
        ins_name = schema_name
          ? `${schema_name}@${db_name}@${instance[0].instance_name}`
          : `${db_name}@${instance[0].instance_name}`
      }
      const tbArr = this.queryTabs.filter(item => item.title === `${tb_name}@${ins_name}`)
      let tbActive = 'qs-0'
      if (tbArr.length === 0) {
        this.downLoading = true
        tbActive = `qs-${+this.queryTabs[this.queryTabs.length - 1].name.replace('qs-', '') + 1}`
        const dbkey = schema_name
          ? `${tb_name}@${schema_name}@${db_name}@${instance_id}`
          : `${tb_name}@${db_name}@${instance_id}`
        await this.$http
          .getInstanceResource({
            id: instance_id,
            resource_type: 'column',
            db_name,
            schema_name,
            tb_name,
            from_workbench_query: true
          })
          .then(res => {
            let cols = []
            res.data.items.forEach(item => {
              // 设置列为叶子节点
              cols.push({
                instance_name: item,
                instance_id: `${instance_id}##${db_name}##${schema_name}##${tb_name}##${item}`,
                leaf: true
              })
            })
            this.$set(this.dbColumns, dbkey, res.data.items)
            this.addCompleter(res.data.items, 'col')
            this.openTableWithData(instance_id, db_name, schema_name, tb_name, res.data.items)
            if (resolve) return resolve(cols)
          })
        await this.$http
          .getTableInfo({
            id: instance_id,
            db_name: db_name,
            schema_name,
            tb_name
          })
          .then(res => {
            if (res.code >= 40000) {
              this.$message({
                showClose: true,
                message: res.message || res.data.msg,
                type: 'warning'
              })
              return
            }
            if (res.data.status === 0) {
              this.$set(this.dbColumnsExtra, dbkey, res.data.data)
              // 左侧菜单打开表格数据，禁用表格标签页上方的条件选择
              this.queryTabs.push({
                instance_id: instance_id,
                instance_name: ins_name,
                db_name: db_name,
                schema_name,
                title: `${tb_name}@${ins_name}`,
                name: tbActive,
                data: res.data.data,
                tbdata: dbkey,
                meta: true,
                menuClick: true,
                formDisabled: true
              })
              this.$set(this.form, 'disabled', true)
            } else {
              this.$message({
                showClose: true,
                message: '获取表元数据异常！',
                type: 'warning'
              })
            }
          })
        this.downLoading = false
      } else {
        tbActive = tbArr[0].name
      }
      this.queryTabsValue = tbActive
    },
    loadNode(node, resolve) {
      if (node.level === 0) {
        this.instanceLoading = true
        this.$http
          .getUserDBInstances()
          .then(res => {
            this.instances = res.data.items
            return resolve(this.instances)
          })
          .finally(() => {
            setTimeout(() => {
              this.instanceLoading = false
            }, 500)
          })
      }
      if (node.level === 1) {
        // 获取数据库
        this.$http
        .getInstanceDatabases({
          id: node.data.instance_id,
          db_name: node.data.instance_name,
          from_workbench_query: true
        })
        .then(res => {
          let dbs = []
            res.data.items.forEach(item => {
              // redis类型直接设置数据库级别为叶子节点
              dbs.push({ leaf: node.data.db_type === 'redis' ? true : false, instance_name: item, instance_id: `${node.data.instance_id}##${item}` })
            })
            return resolve(dbs)
        })

        // this.$http
        //   .getInstanceResource({
        //     id: node.data.instance_id,
        //     db_name: node.data.instance_name,
        //     resource_type: 'database',
        //     from_workbench_query: true
        //   })
        //   .then(res => {
        //     let dbs = []
        //     res.data.items.forEach(item => {
        //       // redis类型直接设置数据库级别为叶子节点
        //       dbs.push({ leaf: node.data.db_type === 'redis' ? true : false, instance_name: item, instance_id: `${node.data.instance_id}##${item}` })
        //     })
        //     return resolve(dbs)
        //   })
      }
      if (node.level === 2) {
        // 获取表或schema
        const instance_id = node.parent.data.instance_id
        const db_name = node.data.instance_name
        if (node.parent.data.db_type === 'pgsql') {
          // pgsql类型获取模式schema
          this.getDbSchemas(instance_id, db_name, resolve)
          return
        }
        // 其它类型获取表
        this.getDbTables(instance_id, db_name, null, resolve)
      }
      if (node.level === 3) {
        // 获取列或表
        const instance_id = node.parent.parent.data.instance_id
        const db_name = node.parent.data.instance_name
        const tb_name = node.data.instance_name
        if (node.parent.parent.data.db_type === 'pgsql') {
          // pgsql类型获取表
          this.getDbTables(instance_id, db_name, tb_name, resolve)
          return
        }
        this.getTbColumns(instance_id, db_name, null, tb_name, resolve)
      }
      if (node.level === 4) {
        // 最后一级菜单，叶子类型(pgsql类型)
        if (!node.isLeaf) {
          const instance_id = node.parent.parent.parent.data.instance_id
          const db_name = node.parent.parent.data.instance_name
          const schema_name = node.parent.data.instance_name
          const tb_name = node.data.instance_name
          this.getTbColumns(instance_id, db_name, schema_name, tb_name, resolve)
        }
      }
      // setTimeout(() => {
      //   resolve(this.instances);
      // }, 500);
    },
    getMaxLength(arr) {
      return arr.reduce((acc, item) => {
        if (item) {
          if (item.length > 1000) {
            return 1000
          }
          const calcLen = this.getTextWidth('' + item)
          if (acc < calcLen) {
            acc = calcLen
          }
        }
        return acc
      }, 0)
    },
    // getTextByStrWidth(str) {
    //     if (!str || (str && typeof str === 'number')) return str
    //     let temp = ''
    //     for (var i=0; i < str.length; i++) {
    //         temp += str[i]
    //         if (this.getTextWidth(temp) > 585) {
    //             temp += '...'
    //             break
    //         }
    //     }
    //     return temp
    // },
    getTextWidth(str) {
      let width = 0
      const html = document.createElement('span')
      html.innerText = str
      html.className = 'getTextWidth'
      document.querySelector('body').appendChild(html)
      width = document.querySelector('.getTextWidth').offsetWidth
      document.querySelector('.getTextWidth').remove()
      return width
    },
    flexColumnWidth(tdata, index, label, prop) {
      //   const arr = tdata.map(x => x[index])
      //   arr.push(label) // 加入表头一起计算宽度
      //   const computedWidth = this.getMaxLength(arr)
      const computedWidth = this.getTextWidth(label) + 65
      //   const realWidth = computedWidth > 580 ? 630 : computedWidth + 50
      // console.log('label 宽度', label, realWidth)
      //   return realWidth + 'px'
      return computedWidth
    },
    // renderHeader(h, { column, $index }) {
    //     let realWidth = 0;
    //     console.log('表格列', column, $index)
    //     let span = document.createElement('span')
    //     span.innerText = column.label
    //     console.log('label表格', column.label)
    //     document.body.appendChild(span)
    //     realWidth = span.getBoundingClientRect().width
    //     column.minWidth = realWidth
    //     document.body.removeChild(span)
    //     console.log('表格宽度', realWidth)
    //     return h('span', column.label)
    // },
    // 表格排序
    sortChange(column, prop, order, instance_id, db_name, schema_name, tb_name, columns) {
      console.log('表格排序', column, prop, order)
      //   this.sortParam = column
      this.openTableWithData(instance_id, db_name, schema_name, tb_name, columns, true, column)
    },
    searchParam() {
      //   this.$nextTick(() => {
      let fieldList = []
      this.matchList = []
      this.matchCount = 0
      //获取所有表头字段
      //   var thChildren = this.$refs.demoWrapper.$el
      //     .querySelector('.is-opened')
      //     .querySelectorAll('.el-submenu__title')
      let thParent = this.$refs[`ss-${this.openIndex}`][0].$el.querySelector('.is-opened')
      if (!thParent) {
        thParent = this.$refs[`ss-${this.openIndex}`][0].$el
      }
      let thChildren = thParent.querySelectorAll('.el-submenu__title')
      console.log('获取所有数据', thChildren)
      thChildren.forEach((item, index) => {
        //获取所有表头字段，放入fieldList数组中
        if (item.innerText) {
          fieldList.push({ name: item.innerText, index: index })
        }
      })
      this.fuzzyQuery(thChildren, fieldList, this.query)
      //   })
    },
    //模糊查询
    fuzzyQuery(thChildren, list, keyWord) {
      var reg = new RegExp(keyWord)
      this.matchList = []
      for (var i = 0; i < list.length; i++) {
        if (reg.test(list[i].name)) {
          this.matchList.push(list[i])
        }
      }
      this.matchCount = this.matchList.length
      console.log('匹配到的', this.matchList)
      if (this.matchCount === 0) return
      this.jumpTarget(thChildren, this.matchList[0])
    },
    jumpTarget(thChildren, matchItem) {
      thChildren.forEach((item, index) => {
        //改变搜索到字段的样式
        if (matchItem.index === index) {
          item.style.backgroundColor = '#e8f4ff'
          item.style.borderBottom = '1px solid red'
        } else {
          item.style.backgroundColor = '#ffffff'
          item.style.borderBottom = 'none'
        }
      })
      this.scrollTopMatch(thChildren, matchItem)
    },
    searchNext() {
      if (!this.query || !this.matchList[0]) return
      if (this.searchIndex === 0) this.searchParam()
      this.searchIndex = this.searchIndex >= this.matchCount - 1 ? 0 : this.searchIndex + 1
      console.log('下个索引 ', this.searchIndex)
      let thParent = this.$refs[`ss-${this.openIndex}`][0].$el.querySelector('.is-opened')
      if (!thParent) {
        thParent = this.$refs[`ss-${this.openIndex}`][0].$el
      }
      let thChildren = thParent.querySelectorAll('.el-submenu__title')
      this.jumpTarget(thChildren, this.matchList[this.searchIndex])
    },
    searchPrev() {
      if (!this.query || !this.matchList[0]) return
      if (this.searchIndex === 0) this.searchParam()
      this.searchIndex = this.searchIndex <= 0 ? this.matchCount - 1 : this.searchIndex - 1
      console.log('上个索引 ', this.searchIndex)
      let thParent = this.$refs[`ss-${this.openIndex}`][0].$el.querySelector('.is-opened')
      if (!thParent) {
        thParent = this.$refs[`ss-${this.openIndex}`][0].$el
      }
      let thChildren = thParent.querySelectorAll('.el-submenu__title')
      this.jumpTarget(thChildren, this.matchList[this.searchIndex])
    },
    scrollTopMatch(thChildren, matchItem) {
      this.$nextTick(() => {
        // document.querySelector('.el-aside').scrollTo({ top: matchItem.offsetTop, behavior: 'smooth'})
        // document.querySelector('.el-aside').scrollTo({
        //   top: this.$refs.demoWrapper.$el
        //     .querySelector('.is-opened')
        //     .querySelectorAll('.el-submenu__title')[matchItem.index].offsetTop,
        //   behavior: 'smooth'
        // })
        // console.log(
        //   '滚动条高度 thChildren[matchItem.index]', matchItem.index,
        //   thChildren[matchItem.index],
        //   thChildren[matchItem.index].offsetTop,
        //   document.querySelector(`.ss-${this.openIndex}-${matchItem.index-1}`).parentElement.offsetTop
        // )
        // console.log('滚动条内容高度', this.$refs.sqlmenu.wrap.scrollHeight, this.$refs.sqlmenu.wrap.clientHeight, thChildren[matchItem.index].offsetTop + this.$refs.sqlmenu.wrap.clientHeight, this.$refs.sqlmenu.wrap.scrollHeight - this.$refs.sqlmenu.wrap.clientHeight, document.querySelector('.dbmenu').offsetTop, document.querySelector(`.htop-${this.openIndex}`).parentElement.offsetTop)

        // 当前实例top document.querySelector(`.htop-${this.openIndex}`).parentElement.offsetTop
        // 元素相对当前实例top thChildren[matchItem.index].offsetTop
        this.$refs.sqlmenu.wrap.scrollTop =
          thChildren[matchItem.index].offsetTop +
          document.querySelector(`.htop-${this.openIndex}`).parentElement.offsetTop

        //    + this.$refs.sqlmenu.wrap.clientHeight
        //   thChildren[matchItem.index].offsetTop + this.$refs.sqlmenu.wrap.clientHeight
        //   this.$refs.sqlmenu.wrap.scrollHeight - thChildren[matchItem.index].offsetTop
        // thChildren[matchItem.index].offsetTop
      })
    },
    showCellContent(row, column, cell, event, columns) {
      //   console.log('row index data', column.label, columns.indexOf(column.label), row[columns.indexOf(column.label)])
      const val = row[columns.indexOf(column.label)]
      if (val.length < 65) return
      const h = this.$createElement
      this.$msgbox({
        customClass: 'dbrowdetail',
        title: column.label,
        message: h('p', null, [h('span', { style: 'font-size: 13px;' }, val)])
      })
    },
    openAdvisorDialog(item) {
      this.form.tool = 3
      this.currentAdvisorItem = item
      this.showAdvisorDialog = true
    },
    jumpWorkflow() {
      this.$router.push({ name: 'workflowrequest' })
    },
    // 历史查询
    queryLogHandle() {},
    downloadData(httpUrl, title, fileType, item) {
      // csv, xlsx
      this.downLoading = true
      const anchor = document.createElement('a')
      document.body.appendChild(anchor)
      const file = `${window.location.origin}${httpUrl}?log_id=${item.log_id}&file_type=${fileType}`
      const headers = new Headers()
      headers.append('Authorization', 'Bearer ' + getToken())
      fetch(file, { headers })
        .then(response => response.blob())
        .then(blobby => {
          const objectUrl = window.URL.createObjectURL(blobby)
          anchor.href = objectUrl
          anchor.download = `${title}.${fileType}`
          anchor.click()
          window.URL.revokeObjectURL(objectUrl)
          this.downLoading = false
        })
        .finally(() => {
          this.downLoading = false
        })
    },
    getData(item) {
      // item.tableData = item.data.rows.slice((item.current_page-1) * item.page_size, item.current_page * item.page_size)
      if (item.data.rows[0]) {
        this.$set(
          item,
          'tableData',
          item.data.rows.slice(
            (item.current_page - 1) * item.page_size,
            item.current_page * item.page_size
          )
        )
      }
    },
    removeTab(targetName) {
      let tabs = this.editableTabs
      let activeName = this.editableTabsValue
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1]
            if (nextTab) {
              activeName = nextTab.name
            }
          }
        })
      }

      this.editableTabsValue = activeName
      this.editableTabs = tabs.filter(tab => tab.name !== targetName)
    },
    tableRowClassName({ row, rowIndex }) {
      // console.log('行数据', row, rowIndex)
      if (row.err_level === 1) {
        return 'warning-row'
      }
      if (row.err_level === 2) {
        return 'error-row'
      }
      return ''
    },
    sqlFormat(item) {
      item.sql_content = format(item.sql_content)
    },
    debounceQuery: _.debounce(function (item, method) {
      this.showAdvisorDialog = false
      this.sqlQuery(item, method)
    }, 500),
    sqlQuery(item, query_type) {
      const methodMap = {
        sqlQuery: 'sqlQuery',
        sqlExplain: 'sqlExplain',
        2: 'sqlAdvisorSqltuning',
        3: 'sqlAdvisorSoar'
      }
      item.data = {}
      item.total = 0
      item.soarData = ''
      item.columns = []
      if (!item.instance_id || !item.db_name) {
        this.$message({
          showClose: true,
          message: '请选择要操作的实例和数据库！',
          type: 'warning'
        })
        return
      }
      if (this.form.instance.db_type === 'pgsql' && !this.form.schema_name) {
        this.$message({
          showClose: true,
          message: '请选择要操作的模式',
          type: 'warning'
        })
        return
      }
      if (!item.sql_content) {
        this.$message({
          showClose: true,
          message: '请输入查询语句！',
          type: 'warning'
        })
        return
      }
      this.downLoading = true
      let sql_content = item.sql_content
      // console.log(
      //   'this.$refs[`sqleditor-${item.name}`]',
      //   this.$refs[`sqleditor-${item.name}`]
      // )
      if (this.$refs[`sqleditor-${item.name}`] && this.$refs[`sqleditor-${item.name}`][0]) {
        sql_content = this.$refs[`sqleditor-${item.name}`][0].editor.session.getTextRange(
          this.$refs[`sqleditor-${item.name}`][0].editor.getSelectionRange()
        )
      }
      console.log('选择的SQL语句', item.sql_content, '####', item)
      this.$http[methodMap[query_type]]({
        id: item.instance_id,
        db_name: item.db_name,
        schema_name: this.form.schema_name,
        sql_content: sql_content || item.sql_content,
        limit_num: this.form.limit_num,
        option: this.form.option
      })
        .then(res => {
          console.log('sql检测结果', res)
          if (res.code >= 40000) {
            this.$message({
              showClose: true,
              message: res.message.msg || res.message,
              type: 'warning'
            })
            this.$set(item, 'errMsg', res.message)
            return
          }
          if (res.data.status === 0) {
            item.log_id = res.data.id
            if (query_type === 3) {
              item.soarData = res.data.data
            } else if (query_type === 2) {
              item.data = JSON.parse(res.data.data)
            } else {
              item.data = res.data.data
              item.total = item.data.rows.length
              item.columns = res.data.data.column_list
              this.getData(item)
            }
            item.errMsg = {}
          } else {
            this.$set(item, 'errMsg', res.data.msg)
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.downLoading = false
          }, 300)
        })
    },
    setLimit(val) {
      const ctab = this.queryTabs.filter(item => item.name === this.queryTabsValue)
      if (!ctab[0]) return
      this.$set(ctab[0], 'limit_num', val || 100)
    },
    setInsMenu(val) {
      // this.defaultOpeneds = [val.instance_id.toString()]
      // this.defaultActive = val.instance_id.toString()
      this.form.db_name = ''
    },
    setDBMenu(val) {
      // // select框改变时使左侧菜单展开
      // this.defaultOpeneds = [this.form.instance.instance_id.toString(), val]
      // this.defaultActive = val
      if (this.form.instance.db_type === 'pgsql') {
        // pgsql类型，获取模式schema
        this.getDbSchemas(this.form.instance.instance_id, val)
      } else {
        // 同时获取当前实例下的表
        // this.getTables(this.form.instance.instance_id, val)
        this.getDbTables(this.form.instance.instance_id, this.form.db_name, this.form.schema_name)
      }
      // 修改tab标签名
      this.queryTabHandle(val, this.form.instance.instance_id)
    },
    setSchemaMenu(val) {
      this.getDbTables(this.form.instance.instance_id, this.form.db_name, this.form.schema_name)
      // 修改tab标签名
      this.queryTabHandle(this.form.db_name, this.form.instance.instance_id, val)
    },
    // debounceEdit: _.debounce(function(targetName, action) {
    //   this.handleTabsEdit(targetName, action)
    // }, 500),
    debounceEdit: _.debounce(function () {
      this.addQueryTab()
    }, 500),
    handleTabsEdit(targetName, action) {
      if (action === 'add') {
        if (!this.form.instance || !this.form.db_name) {
          this.$message({
            showClose: true,
            message: '请先选择要查询的实例和数据库！',
            type: 'warning'
          })
          return
        }
        let tab = this.$options.data().queryTabs[0]
        if (this.form.db_name) {
          tab.title = `${this.form.db_name}@${this.form.instance.instance_name}`
          tab.db_name = this.form.db_name
        }
        tab.name = `qs-${+this.queryTabs[this.queryTabs.length - 1].name.replace('qs-', '') + 1}`
        tab.instance_id = this.form.instance.instance_id
        this.queryTabs.push(tab)
        this.queryTabsValue = tab.name
      }
      if (action === 'remove') {
        if (this.queryTabs.length === 1) {
          this.$message({
            showClose: true,
            message: '最后一个标签页无法移除！',
            type: 'warning'
          })
          return
        }
        let tabs = this.queryTabs
        let activeName = this.queryTabsValue
        if (activeName === targetName) {
          tabs.forEach((tab, index) => {
            if (tab.name === targetName) {
              let nextTab = tabs[index + 1] || tabs[index - 1]
              if (nextTab) {
                activeName = nextTab.name
              }
            }
          })
        }
        this.queryTabsValue = activeName
        this.queryTabs = tabs.filter(tab => tab.name !== targetName)
      }
    },
    addQueryTab() {
      if (!this.form.instance || !this.form.db_name) {
        this.$message({
          showClose: true,
          message: '请先选择要查询的实例和数据库！',
          type: 'warning'
        })
        return
      }
      let tab = this.$options.data().queryTabs[0]
      if (this.form.db_name) {
        tab.title = `${this.form.db_name}@${this.form.instance.instance_name}`
        tab.db_name = this.form.db_name
      }
      tab.name = `qs-${+this.queryTabs[this.queryTabs.length - 1].name.replace('qs-', '') + 1}`
      tab.instance_id = this.form.instance.instance_id
      this.queryTabs.push(tab)
      this.queryTabsValue = tab.name
    },
    removeQueryTab(targetName) {
      if (this.queryTabs.length === 1) {
        this.$message({
          showClose: true,
          message: '最后一个标签页无法移除！',
          type: 'warning'
        })
        return
      }
      let tabs = this.queryTabs
      let activeName = this.queryTabsValue
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1]
            if (nextTab) {
              activeName = nextTab.name
            }
          }
        })
      }
      this.queryTabsValue = activeName
      this.queryTabs = tabs.filter(tab => tab.name !== targetName)
    },
    queryTabHandle(db, instance_id, schema_name) {
      const ctab = this.queryTabs.filter(item => item.name === this.queryTabsValue)
      // 左侧菜单点击打开表格信息和数据，不可更改标题
      if (ctab[0].menuClick) return
      // // 已执行过的标签页不更改标题
      // if(ctab[0].log_id) {
      //   this.$message({
      //     showClose: true,
      //     message: '当前标签页已查询，不能更改数据库.'
      //   })
      //   return
      // }
      const instance = this.instances.filter(item => item.instance_id === instance_id)
      let ctitle = db
      if (schema_name) ctitle = `${schema_name}@${db}`
      if (instance[0]) ctitle += `@${instance[0].instance_name}`
      this.$set(ctab[0], 'title', ctitle)
      ctab[0].instance_id = instance_id
      ctab[0].db_name = db
      ctab[0].limit_num = this.form.limit_num
      ctab[0].formDisabled = this.form.disabled || false
      return
      const instancess = this.instances.filter(item => item.instance_id === instance_id)
      let tab = this.queryTabs.filter(item => item.title === `${db}@${instance[0].instance_name}`)
      console.log('查找tab', tab)
      // 已执行的标签页禁止修改
      if (tab[0] && tab[0].log_id) return
      let title = db
      console.log('查找当前instance', instance)
      if (instance[0]) {
        title = `${db}@${instance[0].instance_name}`
      }
      // tab = this.queryTabs.filter(item => item.name === this.queryTabsValue)
      console.log('查找存在的标签页', tab)
      if (!tab[0]) {
        tab = this.queryTabs.filter(item => item.name === 'qs-0')
      }
      this.$set(tab[0], 'title', title)
      tab[0].instance_id = instance_id
      tab[0].db_name = db
    },
    handleClose(index, indexPath) {
      if (indexPath.length === 2) {
        this.queryTabHandle(index, +indexPath[0])
      }
      if (indexPath.length === 3) {
        this.getTableDetailWithColoumn(indexPath[0], indexPath[1], index)
      }
    },
    handleOpen(index, indexPath) {
      this.matchList = []
      this.matchCount = 0
      console.log('打开菜单索引', index, indexPath)
      this.defaultActive = indexPath[0]
      this.instances.forEach((item, oindex) => {
        if (item.instance_id === +indexPath[0]) this.openIndex = oindex
      })
      if (indexPath.length === 1) {
        // 获取数据库
        // this.getDatabases(index)
        this.query = ''
        return
      }
      if (indexPath.length === 2) {
        // 获取数据表
        this.getTables(indexPath[0], index)
        const insArr = this.instances.filter(item => item.instance_id === +indexPath[0])
        if (insArr.length) this.form.instance = insArr[0]
        this.form.db_name = index
        this.queryTabHandle(index, +indexPath[0])
        this.query = ''
      }
      if (indexPath.length === 3) {
        // 获取表信息和列
        this.getTableDetailWithColoumn(indexPath[0], indexPath[1], index)
      }
    },
    openTableWithData(instance_id, db_name, schema_name, tb_name, columns, resort, sortParam) {
      this.listLoading = true
      console.log('openTableWithData openTableWithData', schema_name, columns)
      const tbdata = schema_name
        ? `${tb_name}@${schema_name}@${db_name}@${instance_id}`
        : `${tb_name}@${db_name}@${instance_id}`
      console.log('openTableWithData openTableWithData tbbbbbb', tbdata)
      if (!this.tableDescData[tbdata]) {
        this.tableDescData[tbdata] = {
          data: [],
          tb_name: tb_name,
          columns: [],
          page_size: 20,
          current_page: 1,
          offset: 0
        }
      }
      let tableItem = this.tableDescData[tbdata]
      if (resort) {
        tableItem.current_page = 1
        // this.sortParam = { prop: 'id', order: 'descending' }
      }
      if (tableItem) tableItem.offset = tableItem.page_size * (tableItem.current_page - 1)
      const sqlContent = '`' + columns.join('`,`') + '`'
      // 默认取第一个列做为排序
      const column = sortParam && sortParam.prop ? sortParam.prop : columns[0]
      const order = sortParam && sortParam.order === 'ascending' ? 'ASC' : 'DESC'
      this.$http
        .sqlQuery({
          click: true,
          id: instance_id,
          db_name: db_name,
          schema_name,
          tb_name,
          columns,
          order_column: column,
          order,
          limit_num: tableItem.page_size,
          offset: tableItem.offset,
          sql_content:
            'select ' +
            sqlContent +
            ` from ${tb_name} ORDER BY ${column} ${order} LIMIT ${tableItem.page_size} OFFSET ${tableItem.offset};`
        })
        .then(res => {
          if (res.code >= 40000) {
            this.$message({
              showClose: true,
              message: res.message.msg || res.message,
              type: 'warning'
            })
            return
          }
          if (res.data.status === 0) {
            this.$set(tableItem, 'data', res.data.data.rows)
            this.$set(tableItem, 'columns', res.data.data.column_list)
          } else {
            this.$message({
              showClose: true,
              message: '查询异常.',
              type: 'warning'
            })
            return
          }
        })
        .finally(() => {
          this.listLoading = false
        })
    },
    getInstances() {
      // 获取用户有权限的实例和数据库
      this.instanceLoading = true
      this.$http
        .getUserDBInstances()
        .then(res => {
          this.instances = res.data.items
        })
        .finally(() => {
          setTimeout(() => {
            this.instanceLoading = false
          }, 500)
        })
    },
    getTables(instance_id, db_name) {
      this.$http
        .getInstanceResource({
          id: instance_id,
          db_name: db_name,
          resource_type: 'table'
        })
        .then(res => {
          // this.tables = res.data.items
          this.$set(this.tables, `${instance_id}##${db_name}`, res.data.items)
          this.form.tb_name = ''
          // this.editableTabs = []
          this.addCompleter(this.tables[`${instance_id}##${db_name}`], 'tb')
          //   // 循环获取表字段
          //   res.data.items.forEach(tb_name => {
          //     this.$http
          //       .getInstanceResource({
          //         id: instance_id,
          //         resource_type: 'column',
          //         db_name,
          //         tb_name
          //       })
          //       .then(res => {
          //         this.$set(
          //           this.dbColumns,
          //           `${tb_name}@${db_name}@${instance_id}`,
          //           res.data.items
          //         )
          //         this.addCompleter(res.data.items, 'col')
          //       })
          //       .finally(() => {
          //         this.descLoading = false
          //       })
          //   })
        })
    },
    async getTableDetailWithColoumn(instance_id, db_name, schema_name, tb_name) {
      let ins_name = `${db_name}@${instance_id}`
      const instance = this.instances.filter(item => item.instance_id === +instance_id)
      if (instance[0]) {
        ins_name = `${db_name}@${instance[0].instance_name}`
      }
      const tbArr = this.queryTabs.filter(item => item.title === `${tb_name}@${ins_name}`)
      let tbActive = 'qs-0'
      if (tbArr.length === 0) {
        this.descLoading = true
        tbActive = `qs-${+this.queryTabs[this.queryTabs.length - 1].name.replace('qs-', '') + 1}`
        await this.$http
          .getInstanceResource({
            id: instance_id,
            resource_type: 'column',
            db_name,
            schema_name,
            tb_name
          })
          .then(res => {
            this.$set(this.dbColumns, `${tb_name}@${db_name}@${instance_id}`, res.data.items)
            this.addCompleter(res.data.items, 'col')
            this.openTableWithData(instance_id, db_name, schema_name, tb_name, res.data.items)
          })
          .finally(() => {
            this.descLoading = false
          })
        await this.$http
          .getTableInfo({
            id: instance_id,
            db_name: db_name,
            tb_name: tb_name
          })
          .then(res => {
            if (res.code >= 40000) {
              this.$message({
                showClose: true,
                message: res.message,
                type: 'warning'
              })
              return
            }
            if (res.data.status === 0) {
              this.queryTabs.push({
                instance_id: instance_id,
                instance_name: ins_name,
                db_name: db_name,
                title: `${tb_name}@${ins_name}`,
                name: tbActive,
                data: res.data.data,
                tbdata: `${tb_name}@${db_name}@${instance_id}`,
                meta: true
              })
            } else {
              this.$message({
                showClose: true,
                message: '获取表元数据异常！',
                type: 'warning'
              })
            }
          })
      } else {
        tbActive = tbArr[0].name
      }
      this.queryTabsValue = tbActive
    },
    addCompleter(data, type) {
      const metaMap = { tb: 'Table', col: 'Column' }
      let completions = []
      data.forEach(element => {
        completions.push({
          name: element,
          value: element,
          score: 1000,
          meta: element
        })
      })
      var customCompleter = {
        getCompletions: function (editor, session, pos, prefix, callback) {
          callback(null, [
            ...data.map(function (item) {
              return {
                caption: item,
                value: item,
                meta: metaMap[type]
              }
            })
          ])
        }
      }
      // const langTools = ace.acequire('ace/ext/language_tools')
      // langTools.addCompleter(customCompleter)
      const editorRef = this.$refs[`sqleditor-${this.queryTabsValue}`]
      let completerIdx = this.completerIndex[type][this.queryTabsValue]
      if (editorRef && editorRef[0]) {
        if (!completerIdx) {
          editorRef[0].editor.completers.push(customCompleter)
          completerIdx = editorRef[0].editor.completers.length
        } else {
          editorRef[0].editor.completers[completerIdx - 1] = customCompleter
        }
      }
    }
  }
}
</script>

<style scoped>
.el-aside >>> .el-submenu__title {
  height: 40px !important;
  line-height: 40px !important;
  font-size: 16px !important;
  padding-left: 0 !important;
}
/* .el-aside >>> .is-opened div {
  color: #409eff !important;
} */
/* .el-aside >>> .el-submenu__title:active {
  color: #e8f4ff;
} */
</style>
<style scoped>
.el-tabs >>> .el-tabs__new-tab {
  color: #409eff;
  border: none;
  font-size: 24px;
  font-weight: bolder;
  height: 28px;
  width: 28px;
  line-height: 28px;
  margin: 12px 0 9px 10px;
}
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
.el-submenu .el-menu-item {
  height: 24px !important;
  line-height: 24px !important;
  margin-left: 44px !important;
  padding-left: 0 !important;
}
.el-table >>> .cell {
  white-space: nowrap;
  width: fit-content;
}
.el-table >>> th {
  white-space: nowrap;
  width: fit-content;
}
.el-table >>> td {
  white-space: nowrap;
  width: fit-content;
}
.el-table .warning-row {
  background: oldlace;
}
.el-table .warning-row:hover {
  color: #606266;
}
.el-table .error-row {
  background: #f56c6c;
  color: #ffffff;
}
.el-table .error-row:hover {
  color: #606266;
}
.errmsg {
  color: #f56c6c;
  font-size: 14px;
  margin-bottom: 15px;
  /* margin-left: 120px; */
}
.leftsearch {
  width: 350px;
  position: fixed !important;
  top: 71px;
  padding-bottom: 20px;
  z-index: 99999999999999999999;
  background: #ffffff;
  padding-top: 10px;
  /* padding-left: 20px;
    padding-right: 20px; */
  border-right: solid 1px #e6e6e6 !important;
}
.leftsearch >>> .searchbtn {
  font-size: 16px;
}
.leftsearch >>> input {
  border-left: none;
  border-right: none;
  border-top: none;
  padding-left: 30px;
  border-bottom: 1px solid #ff4949;
  border-radius: unset;
}
.leftsearch >>> .el-input-group__append {
  background-color: #fff;
  border-bottom: 1px solid red;
  border-top: none;
  border-right: none;
  border-radius: unset;
}
/* .leftsearch >>> input::placeholder {
    padding-left: 10px;
} */
/* 美化滚动条 */
.ve-tree >>> .vue-recycle-scroller::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.ve-tree >>> .vue-recycle-scroller::-webkit-scrollbar-track {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(238, 238, 238, 0.3);
  background-color: #eeeeee;
}
.ve-tree >>> .vue-recycle-scroller::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(145, 143, 0143, 0.3);
  background-color: rgba(144, 147, 153, 0.5);
  /* transform: translateY(3.08748%); */
}
</style>
<style>
.dbrowdetail {
  width: 80% !important;
  height: 100vh;
  overflow-y: scroll;
  white-space: inherit;
}
</style>