package controllers

import (
	"net/http"
	"strconv"

	"github.com/devops-microservices/sql-service/models"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// BaseController 基础控制器
type BaseController struct {
	log *logrus.Logger
}

// NewBaseController 创建基础控制器
func NewBaseController(log *logrus.Logger) *BaseController {
	return &BaseController{
		log: log,
	}
}

// SuccessResponse 成功响应
func (bc *BaseController) SuccessResponse(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, models.APIResponse{
		Code:    200,
		Message: "success",
		Data:    data,
	})
}

// ErrorResponse 错误响应
func (bc *BaseController) ErrorResponse(c *gin.Context, code int, message string) {
	c.JSON(http.StatusOK, models.APIResponse{
		Code:    code,
		Message: message,
	})
}

// BadRequestResponse 400错误响应
func (bc *BaseController) BadRequestResponse(c *gin.Context, message string) {
	bc.ErrorResponse(c, 400, message)
}

// NotFoundResponse 404错误响应
func (bc *BaseController) NotFoundResponse(c *gin.Context, message string) {
	bc.ErrorResponse(c, 404, message)
}

// InternalErrorResponse 500错误响应
func (bc *BaseController) InternalErrorResponse(c *gin.Context, message string) {
	bc.ErrorResponse(c, 500, message)
}

// GetPaginationParams 获取分页参数
func (bc *BaseController) GetPaginationParams(c *gin.Context) (page, pageSize int) {
	page = 1
	pageSize = 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if sizeStr := c.Query("page_size"); sizeStr != "" {
		if s, err := strconv.Atoi(sizeStr); err == nil && s > 0 && s <= 100 {
			pageSize = s
		}
	}

	return page, pageSize
}

// GetSearchParam 获取搜索参数
func (bc *BaseController) GetSearchParam(c *gin.Context) string {
	return c.Query("search")
}

// GetIDParam 获取ID参数
func (bc *BaseController) GetIDParam(c *gin.Context) (uint, error) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return 0, err
	}
	return uint(id), nil
}

// PaginatedResponse 分页响应
func (bc *BaseController) PaginatedResponse(c *gin.Context, items interface{}, total int64, page, pageSize int) {
	totalPages := int(total) / pageSize
	if int(total)%pageSize > 0 {
		totalPages++
	}

	response := models.PaginatedResponse{
		Items:      items,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}

	bc.SuccessResponse(c, response)
}

// ValidateJSON 验证JSON请求体
func (bc *BaseController) ValidateJSON(c *gin.Context, obj interface{}) bool {
	if err := c.ShouldBindJSON(obj); err != nil {
		bc.BadRequestResponse(c, "请求参数格式错误: "+err.Error())
		return false
	}
	return true
}

// GetCurrentUser 获取当前用户（从JWT或其他认证方式）
func (bc *BaseController) GetCurrentUser(c *gin.Context) string {
	// 从JWT token或其他认证方式获取用户信息

	// 1. 尝试从context中获取用户信息（中间件设置）
	if user, exists := c.Get("user"); exists {
		if userStr, ok := user.(string); ok {
			return userStr
		}
	}

	// 2. 从Authorization header中解析JWT token
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		// 移除 "Bearer " 前缀
		if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
			token := authHeader[7:]
			if username := bc.parseJWTToken(token); username != "" {
				return username
			}
		}
	}

	// 3. 从X-Username header中获取（用于内部服务调用）
	if username := c.GetHeader("X-Username"); username != "" {
		return username
	}

	// 4. 开发环境默认用户
	return "admin"
}

// IsAdmin 检查是否为管理员
func (bc *BaseController) IsAdmin(c *gin.Context) bool {
	// 实现管理员权限检查逻辑

	// 1. 尝试从context中获取权限信息（中间件设置）
	if isAdmin, exists := c.Get("is_admin"); exists {
		if adminBool, ok := isAdmin.(bool); ok {
			return adminBool
		}
	}

	// 2. 从JWT token中解析权限信息
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
			token := authHeader[7:]
			if isAdmin := bc.parseJWTTokenForAdmin(token); isAdmin {
				return true
			}
		}
	}

	// 3. 从X-Is-Admin header中获取（用于内部服务调用）
	if isAdmin := c.GetHeader("X-Is-Admin"); isAdmin == "true" {
		return true
	}

	// 4. 检查用户名是否为管理员
	username := bc.GetCurrentUser(c)
	return bc.isAdminUser(username)
}

// parseJWTToken 解析JWT token获取用户名
func (bc *BaseController) parseJWTToken(token string) string {
	// 简化实现：这里应该使用JWT库来解析token
	// 实际生产环境中应该验证token签名和过期时间

	// 暂时从token中提取用户信息（假设token格式为base64编码的JSON）
	// 实际应用中应该使用 github.com/golang-jwt/jwt 等库

	// 这里返回空字符串，表示需要使用其他方式获取用户信息
	return ""
}

// parseJWTTokenForAdmin 解析JWT token获取管理员权限
func (bc *BaseController) parseJWTTokenForAdmin(token string) bool {
	// 简化实现：这里应该使用JWT库来解析token中的权限信息
	// 实际生产环境中应该验证token签名和过期时间

	// 暂时返回false，表示需要使用其他方式检查权限
	return false
}

// isAdminUser 检查用户名是否为管理员
func (bc *BaseController) isAdminUser(username string) bool {
	// 定义管理员用户列表
	adminUsers := []string{
		"admin",
		"root",
		"administrator",
		"dba",
		"superuser",
	}

	for _, adminUser := range adminUsers {
		if username == adminUser {
			return true
		}
	}

	// 也可以从配置文件或数据库中获取管理员列表
	return false
}

// LogRequest 记录请求日志
func (bc *BaseController) LogRequest(c *gin.Context, action string) {
	bc.log.WithFields(logrus.Fields{
		"method":     c.Request.Method,
		"path":       c.Request.URL.Path,
		"ip":         c.ClientIP(),
		"user_agent": c.GetHeader("User-Agent"),
		"action":     action,
		"user":       bc.GetCurrentUser(c),
	}).Info("API Request")
}
