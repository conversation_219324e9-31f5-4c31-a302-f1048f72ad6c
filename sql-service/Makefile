# SQL Service Makefile

# 变量定义
APP_NAME = sql-service
VERSION = 1.0.0
BUILD_TIME = $(shell date +%Y-%m-%d\ %H:%M:%S)
GIT_COMMIT = $(shell git rev-parse --short HEAD)
GO_VERSION = $(shell go version | awk '{print $$3}')

# 构建标志
LDFLAGS = -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

# 默认目标
.PHONY: all
all: clean build

# 清理
.PHONY: clean
clean:
	@echo "🧹 清理构建文件..."
	@rm -f $(APP_NAME)
	@rm -rf dist/
	@go clean

# 安装依赖
.PHONY: deps
deps:
	@echo "📦 安装依赖..."
	@go mod download
	@go mod tidy

# 代码格式化
.PHONY: fmt
fmt:
	@echo "🎨 格式化代码..."
	@go fmt ./...
	@goimports -w .

# 代码检查
.PHONY: lint
lint:
	@echo "🔍 代码检查..."
	@golangci-lint run

# 生成Swagger文档
.PHONY: swag
swag:
	@echo "📖 生成Swagger文档..."
	@swag init -g main.go -o docs/

# 构建
.PHONY: build
build: deps swag
	@echo "🔨 构建应用..."
	@CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(APP_NAME) .
	@echo "✅ 构建完成: $(APP_NAME)"

# 本地构建
.PHONY: build-local
build-local: deps swag
	@echo "🔨 本地构建..."
	@go build $(LDFLAGS) -o $(APP_NAME) .
	@echo "✅ 本地构建完成: $(APP_NAME)"

# 运行
.PHONY: run
run: build-local
	@echo "🚀 启动服务..."
	@./$(APP_NAME)

# 开发模式运行
.PHONY: dev
dev:
	@echo "🔧 开发模式启动..."
	@go run main.go

# 测试
.PHONY: test
test:
	@echo "🧪 运行测试..."
	@go test -v ./...

# 测试覆盖率
.PHONY: test-coverage
test-coverage:
	@echo "📊 测试覆盖率..."
	@go test -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "📊 覆盖率报告: coverage.html"

# 基准测试
.PHONY: bench
bench:
	@echo "⚡ 基准测试..."
	@go test -bench=. -benchmem ./...

# Docker构建
.PHONY: docker-build
docker-build:
	@echo "🐳 构建Docker镜像..."
	@docker build -t $(APP_NAME):$(VERSION) .
	@docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest
	@echo "✅ Docker镜像构建完成"

# Docker运行
.PHONY: docker-run
docker-run:
	@echo "🐳 运行Docker容器..."
	@docker run -d \
		--name $(APP_NAME) \
		-p 9983:9983 \
		-v $(PWD)/config.yaml:/app/config.yaml \
		$(APP_NAME):latest

# Docker Compose
.PHONY: docker-up
docker-up:
	@echo "🐳 启动Docker Compose..."
	@docker-compose up -d

.PHONY: docker-down
docker-down:
	@echo "🐳 停止Docker Compose..."
	@docker-compose down

.PHONY: docker-logs
docker-logs:
	@echo "📋 查看Docker日志..."
	@docker-compose logs -f $(APP_NAME)

# 数据库迁移
.PHONY: migrate
migrate:
	@echo "🗄️ 数据库迁移..."
	@./$(APP_NAME) --force-migrate

# 安装工具
.PHONY: install-tools
install-tools:
	@echo "🛠️ 安装开发工具..."
	@go install github.com/swaggo/swag/cmd/swag@latest
	@go install golang.org/x/tools/cmd/goimports@latest
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# 发布
.PHONY: release
release: clean test build
	@echo "📦 创建发布包..."
	@mkdir -p dist
	@cp $(APP_NAME) dist/
	@cp config.yaml.example dist/config.yaml
	@cp README.md dist/
	@cp start.sh dist/
	@chmod +x dist/start.sh
	@tar -czf dist/$(APP_NAME)-$(VERSION)-linux-amd64.tar.gz -C dist .
	@echo "✅ 发布包创建完成: dist/$(APP_NAME)-$(VERSION)-linux-amd64.tar.gz"

# 交叉编译
.PHONY: build-all
build-all: deps swag
	@echo "🔨 交叉编译..."
	@mkdir -p dist
	@GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o dist/$(APP_NAME)-linux-amd64 .
	@GOOS=linux GOARCH=arm64 go build $(LDFLAGS) -o dist/$(APP_NAME)-linux-arm64 .
	@GOOS=darwin GOARCH=amd64 go build $(LDFLAGS) -o dist/$(APP_NAME)-darwin-amd64 .
	@GOOS=darwin GOARCH=arm64 go build $(LDFLAGS) -o dist/$(APP_NAME)-darwin-arm64 .
	@GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o dist/$(APP_NAME)-windows-amd64.exe .
	@echo "✅ 交叉编译完成"

# 健康检查
.PHONY: health
health:
	@echo "🏥 健康检查..."
	@curl -f http://localhost:9983/health || echo "❌ 服务不健康"

# 查看API文档
.PHONY: docs
docs:
	@echo "📖 打开API文档..."
	@open http://localhost:9983/swagger/index.html

# 性能分析
.PHONY: profile
profile:
	@echo "📈 性能分析..."
	@go tool pprof http://localhost:9983/debug/pprof/profile

# 内存分析
.PHONY: memprofile
memprofile:
	@echo "🧠 内存分析..."
	@go tool pprof http://localhost:9983/debug/pprof/heap

# 帮助
.PHONY: help
help:
	@echo "SQL Service Makefile"
	@echo ""
	@echo "可用命令:"
	@echo "  build          构建应用"
	@echo "  build-local    本地构建"
	@echo "  build-all      交叉编译"
	@echo "  run            运行应用"
	@echo "  dev            开发模式运行"
	@echo "  test           运行测试"
	@echo "  test-coverage  测试覆盖率"
	@echo "  bench          基准测试"
	@echo "  clean          清理构建文件"
	@echo "  deps           安装依赖"
	@echo "  fmt            格式化代码"
	@echo "  lint           代码检查"
	@echo "  swag           生成Swagger文档"
	@echo "  docker-build   构建Docker镜像"
	@echo "  docker-run     运行Docker容器"
	@echo "  docker-up      启动Docker Compose"
	@echo "  docker-down    停止Docker Compose"
	@echo "  docker-logs    查看Docker日志"
	@echo "  migrate        数据库迁移"
	@echo "  install-tools  安装开发工具"
	@echo "  release        创建发布包"
	@echo "  health         健康检查"
	@echo "  docs           打开API文档"
	@echo "  profile        性能分析"
	@echo "  memprofile     内存分析"
	@echo "  help           显示帮助"
