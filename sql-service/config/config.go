package config

import (
	"fmt"
	"os"
	"time"

	"github.com/devops-microservices/sql-service/models"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Config 应用配置结构
type Config struct {
	Server         ServerConfig         `mapstructure:"server"`
	Database       DatabaseConfig       `mapstructure:"database"`
	Redis          RedisConfig          `mapstructure:"redis"`
	Scheduler      SchedulerConfig      `mapstructure:"scheduler"`
	SqlQuery       SqlQueryConfig       `mapstructure:"sql_query"`
	DataMasking    DataMaskingConfig    `mapstructure:"data_masking"`
	Log            LogConfig            `mapstructure:"log"`
	UCenterService UCenterServiceConfig `mapstructure:"ucenter_service"`
	Tools          ToolsConfig          `mapstructure:"tools"`
	GoInception    GoInceptionConfig    `mapstructure:"goinception"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
	IdleTimeout  int    `mapstructure:"idle_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type            string `mapstructure:"type"`
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	Username        string `mapstructure:"username"`
	Password        string `mapstructure:"password"`
	DBName          string `mapstructure:"dbname"`
	SSLMode         string `mapstructure:"sslmode"`
	Timezone        string `mapstructure:"timezone"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
	PoolSize int    `mapstructure:"pool_size"`
}

// SchedulerConfig 任务调度配置
type SchedulerConfig struct {
	Enabled  bool   `mapstructure:"enabled"`
	Timezone string `mapstructure:"timezone"`
}

// SqlQueryConfig SQL查询配置
type SqlQueryConfig struct {
	MaxExecutionTime int  `mapstructure:"max_execution_time"`
	DefaultLimit     int  `mapstructure:"default_limit"`
	MaxLimit         int  `mapstructure:"max_limit"`
	DisableStar      bool `mapstructure:"disable_star"`
	DataMasking      bool `mapstructure:"data_masking"`
	QueryCheck       bool `mapstructure:"query_check"`
}

// DataMaskingConfig 数据脱敏配置
type DataMaskingConfig struct {
	Enabled bool                    `mapstructure:"enabled"`
	Rules   []DataMaskingRuleConfig `mapstructure:"rules"`
}

// DataMaskingRuleConfig 脱敏规则配置
type DataMaskingRuleConfig struct {
	Name        string `mapstructure:"name"`
	Regex       string `mapstructure:"regex"`
	Replacement string `mapstructure:"replacement"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// UCenterServiceConfig 用户中心服务配置
type UCenterServiceConfig struct {
	URL              string `mapstructure:"url"`
	Timeout          int    `mapstructure:"timeout"`
	SkipAuthenticate bool   `mapstructure:"skip_authenticate"`
}

// ToolsConfig 外部工具配置
type ToolsConfig struct {
	Soar      SoarConfig      `mapstructure:"soar"`
	SqlTuning SqlTuningConfig `mapstructure:"sqltuning"`
}

// SoarConfig SOAR工具配置
type SoarConfig struct {
	Path    string `mapstructure:"path"`
	Enabled bool   `mapstructure:"enabled"`
}

// SqlTuningConfig SQL调优配置
type SqlTuningConfig struct {
	Enabled bool `mapstructure:"enabled"`
}

// GoInceptionConfig GoInception配置
type GoInceptionConfig struct {
	Enabled  bool   `mapstructure:"enabled"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	// 设置默认值
	setDefaults()

	// 设置配置文件路径
	if configPath != "" {
		viper.SetConfigFile(configPath)
	} else {
		viper.SetConfigName("config")
		viper.SetConfigType("yaml")
		viper.AddConfigPath(".")
		viper.AddConfigPath("./config")
		viper.AddConfigPath("/etc/sql-service")
	}

	// 支持环境变量
	viper.AutomaticEnv()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// 配置文件未找到，使用默认配置
			logrus.Warn("配置文件未找到，使用默认配置")
		} else {
			return nil, fmt.Errorf("读取配置文件失败: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	// 服务器默认配置
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 9983)
	viper.SetDefault("server.read_timeout", 60)
	viper.SetDefault("server.write_timeout", 60)
	viper.SetDefault("server.idle_timeout", 60)

	// 数据库默认配置 - 使用PostgreSQL
	viper.SetDefault("database.type", "postgres")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.username", "postgres")
	viper.SetDefault("database.password", "")
	viper.SetDefault("database.dbname", "opsdb")
	viper.SetDefault("database.sslmode", "disable")
	viper.SetDefault("database.timezone", "Asia/Shanghai")
	viper.SetDefault("database.max_idle_conns", 10)
	viper.SetDefault("database.max_open_conns", 100)
	viper.SetDefault("database.conn_max_lifetime", 3600)

	// Redis默认配置
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)
	viper.SetDefault("redis.pool_size", 10)

	// 任务调度默认配置
	viper.SetDefault("scheduler.enabled", true)
	viper.SetDefault("scheduler.timezone", "Asia/Shanghai")

	// SQL查询默认配置
	viper.SetDefault("sql_query.max_execution_time", 60)
	viper.SetDefault("sql_query.default_limit", 100)
	viper.SetDefault("sql_query.max_limit", 10000)
	viper.SetDefault("sql_query.disable_star", false)
	viper.SetDefault("sql_query.data_masking", false)
	viper.SetDefault("sql_query.query_check", false)

	// 数据脱敏默认配置
	viper.SetDefault("data_masking.enabled", false)

	// 日志默认配置
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "text")
	viper.SetDefault("log.output", "stdout")

	// UCenterService默认配置
	viper.SetDefault("ucenter_service.url", "http://ucenter-service:9982")
	viper.SetDefault("ucenter_service.timeout", 5)
	viper.SetDefault("ucenter_service.skip_authenticate", false)

	// 工具默认配置
	viper.SetDefault("tools.soar.path", "/usr/local/bin/soar")
	viper.SetDefault("tools.soar.enabled", false)
	viper.SetDefault("tools.sqltuning.enabled", true)

	// GoInception默认配置
	viper.SetDefault("goinception.enabled", true)
	viper.SetDefault("goinception.host", "localhost")
	viper.SetDefault("goinception.port", 4000)
	viper.SetDefault("goinception.user", "root")
	viper.SetDefault("goinception.password", "")
}

// ValidateConfig 验证配置
func ValidateConfig(config *Config) error {
	// 验证服务器配置
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		return fmt.Errorf("无效的服务器端口: %d", config.Server.Port)
	}

	// 验证数据库配置
	if config.Database.Type == "" {
		return fmt.Errorf("数据库类型不能为空")
	}

	supportedDBTypes := []string{"mysql", "postgres", "sqlite"}
	isSupported := false
	for _, dbType := range supportedDBTypes {
		if config.Database.Type == dbType {
			isSupported = true
			break
		}
	}
	if !isSupported {
		return fmt.Errorf("不支持的数据库类型: %s", config.Database.Type)
	}

	// 验证日志级别
	supportedLogLevels := []string{"debug", "info", "warn", "error"}
	isValidLogLevel := false
	for _, level := range supportedLogLevels {
		if config.Log.Level == level {
			isValidLogLevel = true
			break
		}
	}
	if !isValidLogLevel {
		return fmt.Errorf("不支持的日志级别: %s", config.Log.Level)
	}

	return nil
}

// InitDatabase 初始化数据库连接
func InitDatabase(config *DatabaseConfig, log *logrus.Logger) (*gorm.DB, error) {
	var dsn string
	var dialector gorm.Dialector

	switch config.Type {
	case "mysql":
		dsn = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=%s",
			config.Username, config.Password, config.Host, config.Port, config.DBName, config.Timezone)
		dialector = mysql.Open(dsn)
	case "postgres":
		dsn = fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=%s TimeZone=%s",
			config.Host, config.Username, config.Password, config.DBName, config.Port, config.SSLMode, config.Timezone)
		dialector = postgres.Open(dsn)
	case "sqlite":
		dsn = config.DBName
		if dsn == "" {
			dsn = "sql-service.db"
		}
		dialector = sqlite.Open(dsn)
	default:
		return nil, fmt.Errorf("不支持的数据库类型: %s", config.Type)
	}

	// 配置GORM日志
	var gormLogLevel logger.LogLevel
	switch log.Level {
	case logrus.DebugLevel:
		gormLogLevel = logger.Info
	case logrus.InfoLevel:
		gormLogLevel = logger.Warn
	default:
		gormLogLevel = logger.Error
	}

	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(gormLogLevel),
	}

	db, err := gorm.Open(dialector, gormConfig)
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取底层sql.DB对象进行连接池配置
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库连接失败: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(config.MaxIdleConns)
	sqlDB.SetMaxOpenConns(config.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(time.Duration(config.ConnMaxLifetime) * time.Second)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %w", err)
	}

	log.Infof("✅ 数据库连接成功 (%s)", config.Type)
	return db, nil
}

// AutoMigrate 执行数据库迁移
func AutoMigrate(db *gorm.DB, log *logrus.Logger) error {
	log.Info("开始执行数据库迁移...")

	if err := models.AutoMigrate(db); err != nil {
		return fmt.Errorf("数据库迁移失败: %w", err)
	}

	log.Info("✅ 数据库迁移完成")
	return nil
}

// GetEnv 获取环境变量，如果不存在则返回默认值
func GetEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
