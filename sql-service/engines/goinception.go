package engines

import (
	"database/sql"
	"fmt"
	"strings"

	"github.com/devops-microservices/sql-service/config"
	"github.com/devops-microservices/sql-service/models"
	_ "github.com/go-sql-driver/mysql"
)

// GoInceptionEngine GoInception SQL审核引擎
type GoInceptionEngine struct {
	config   *config.GoInceptionConfig
	instance *models.DBInstance
	conn     *sql.DB
}

// NewGoInceptionEngine 创建GoInception引擎
func NewGoInceptionEngine(cfg *config.GoInceptionConfig, instance *models.DBInstance) *GoInceptionEngine {
	return &GoInceptionEngine{
		config:   cfg,
		instance: instance,
	}
}

// GetConnection 获取GoInception连接
func (g *GoInceptionEngine) GetConnection() (*sql.DB, error) {
	if g.conn != nil {
		return g.conn, nil
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/",
		g.config.User, g.config.Password, g.config.Host, g.config.Port)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("连接GoInception失败: %w", err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("GoInception连接测试失败: %w", err)
	}

	g.conn = db
	return db, nil
}

// Close 关闭连接
func (g *GoInceptionEngine) Close() error {
	if g.conn != nil {
		return g.conn.Close()
	}
	return nil
}

// CheckSQL 使用GoInception检查SQL
func (g *GoInceptionEngine) CheckSQL(dbName, sql string) (*ReviewSet, error) {
	db, err := g.GetConnection()
	if err != nil {
		return nil, err
	}

	// 构建GoInception检查SQL
	inceptionSQL := g.buildInceptionSQL(dbName, sql, false)

	rows, err := db.Query(inceptionSQL)
	if err != nil {
		return nil, fmt.Errorf("GoInception检查失败: %w", err)
	}
	defer rows.Close()

	return g.parseInceptionResult(rows, sql)
}

// ExecuteSQL 使用GoInception执行SQL
func (g *GoInceptionEngine) ExecuteSQL(dbName, sql string) (*ReviewSet, error) {
	db, err := g.GetConnection()
	if err != nil {
		return nil, err
	}

	// 构建GoInception执行SQL
	inceptionSQL := g.buildInceptionSQL(dbName, sql, true)

	rows, err := db.Query(inceptionSQL)
	if err != nil {
		return nil, fmt.Errorf("GoInception执行失败: %w", err)
	}
	defer rows.Close()

	return g.parseInceptionResult(rows, sql)
}

// buildInceptionSQL 构建GoInception SQL
func (g *GoInceptionEngine) buildInceptionSQL(dbName, sql string, execute bool) string {
	var builder strings.Builder

	// 设置GoInception参数
	builder.WriteString("/*--user=")
	builder.WriteString(g.instance.User)
	builder.WriteString(";--password=")
	builder.WriteString(g.instance.Password)
	builder.WriteString(";--host=")
	builder.WriteString(g.instance.Host)
	builder.WriteString(";--port=")
	builder.WriteString(fmt.Sprintf("%d", g.instance.Port))
	builder.WriteString(";--check=1")

	if execute {
		builder.WriteString(";--execute=1")
	} else {
		builder.WriteString(";--execute=0")
	}

	builder.WriteString(";--backup=0") // 暂时不启用备份
	builder.WriteString("*/\n")

	// 添加inception_magic_start
	builder.WriteString("inception_magic_start;\n")

	// 添加use database
	builder.WriteString("use ")
	builder.WriteString(dbName)
	builder.WriteString(";\n")

	// 添加SQL语句
	builder.WriteString(sql)
	if !strings.HasSuffix(sql, ";") {
		builder.WriteString(";")
	}
	builder.WriteString("\n")

	// 添加inception_magic_commit
	builder.WriteString("inception_magic_commit;")

	return builder.String()
}

// parseInceptionResult 解析GoInception结果
func (g *GoInceptionEngine) parseInceptionResult(rows *sql.Rows, originalSQL string) (*ReviewSet, error) {
	columns, err := rows.Columns()
	if err != nil {
		return nil, err
	}

	var results []ReviewResult
	var warningCount, errorCount int
	var isCritical bool
	var warning, errorMsg *string

	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, err
		}

		// 转换为字符串
		stringValues := make([]string, len(values))
		for i, val := range values {
			if val != nil {
				if b, ok := val.([]byte); ok {
					stringValues[i] = string(b)
				} else {
					stringValues[i] = fmt.Sprintf("%v", val)
				}
			}
		}

		// 解析GoInception结果格式
		// 通常格式为: order_id, stage, error_level, stage_status, error_message, sql, affected_rows, sequence
		if len(stringValues) >= 8 {
			orderID := parseInt(stringValues[0])
			stage := stringValues[1]
			errLevel := parseInt(stringValues[2])
			stageStatus := stringValues[3]
			errorMessage := stringValues[4]
			sql := stringValues[5]
			affectedRows := parseInt64(stringValues[6])
			sequence := stringValues[7]

			result := ReviewResult{
				ID:           orderID,
				Stage:        stage,
				ErrLevel:     errLevel,
				StageStatus:  stageStatus,
				ErrorMessage: errorMessage,
				SQL:          sql,
				AffectedRows: affectedRows,
				Sequence:     sequence,
				ExecuteTime:  "0",
			}

			results = append(results, result)

			// 统计错误和警告
			if errLevel == 1 {
				warningCount++
				if warning == nil {
					warning = &errorMessage
				}
			} else if errLevel == 2 {
				errorCount++
				isCritical = true
				if errorMsg == nil {
					errorMsg = &errorMessage
				}
			}
		}
	}

	reviewSet := &ReviewSet{
		FullSQL:      originalSQL,
		IsExecute:    errorCount == 0,
		Checked:      &[]bool{true}[0],
		Warning:      warning,
		Error:        errorMsg,
		WarningCount: warningCount,
		ErrorCount:   errorCount,
		IsCritical:   isCritical,
		SyntaxType:   ParseSQLType(originalSQL),
		Rows:         results,
		AffectedRows: 0,
	}

	// 计算总影响行数
	for _, result := range results {
		reviewSet.AffectedRows += result.AffectedRows
	}

	return reviewSet, nil
}

// parseInt 解析整数
func parseInt(s string) int {
	if s == "" {
		return 0
	}
	var result int
	fmt.Sscanf(s, "%d", &result)
	return result
}

// parseInt64 解析int64
func parseInt64(s string) int64 {
	if s == "" {
		return 0
	}
	var result int64
	fmt.Sscanf(s, "%d", &result)
	return result
}
