#!/bin/bash

# SQL Service 测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:9983"
API_BASE="$BASE_URL/api/sql"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# 检查服务是否运行
check_service() {
    log_info "检查服务状态..."
    
    if curl -f -s "$BASE_URL/health" > /dev/null; then
        log_info "✅ 服务运行正常"
        return 0
    else
        log_error "❌ 服务未运行或不健康"
        return 1
    fi
}

# 测试健康检查
test_health() {
    log_test "测试健康检查接口..."
    
    response=$(curl -s "$BASE_URL/health")
    if echo "$response" | grep -q '"status":"ok"'; then
        log_info "✅ 健康检查通过"
    else
        log_error "❌ 健康检查失败"
        echo "响应: $response"
    fi
}

# 测试Swagger文档
test_swagger() {
    log_test "测试Swagger文档..."
    
    if curl -f -s "$BASE_URL/swagger/index.html" > /dev/null; then
        log_info "✅ Swagger文档可访问"
    else
        log_error "❌ Swagger文档不可访问"
    fi
}

# 测试数据库实例API
test_instance_api() {
    log_test "测试数据库实例API..."
    
    # 获取实例列表
    log_info "获取数据库实例列表..."
    response=$(curl -s "$API_BASE/instance")
    if echo "$response" | grep -q '"code":200'; then
        log_info "✅ 获取实例列表成功"
    else
        log_error "❌ 获取实例列表失败"
        echo "响应: $response"
    fi
    
    # 创建测试实例
    log_info "创建测试数据库实例..."
    create_data='{
        "instance_name": "test-instance",
        "alias": "测试实例",
        "type": "master",
        "db_type": "sqlite",
        "mode": "standalone",
        "host": "localhost",
        "port": 0,
        "user": "",
        "password": "",
        "db_name": "test.db",
        "charset": "utf8",
        "auto_backup": 0,
        "projects": {"Data": ["test"]},
        "environments": {"Data": ["development"]}
    }'
    
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$create_data" \
        "$API_BASE/instance")
    
    if echo "$response" | grep -q '"code":200'; then
        log_info "✅ 创建实例成功"
        # 提取实例ID
        instance_id=$(echo "$response" | grep -o '"id":[0-9]*' | cut -d':' -f2)
        log_info "实例ID: $instance_id"
        
        # 测试获取实例详情
        if [ -n "$instance_id" ]; then
            log_info "获取实例详情..."
            detail_response=$(curl -s "$API_BASE/instance/$instance_id")
            if echo "$detail_response" | grep -q '"code":200'; then
                log_info "✅ 获取实例详情成功"
            else
                log_error "❌ 获取实例详情失败"
            fi
            
            # 测试连接
            log_info "测试数据库连接..."
            test_response=$(curl -s -X POST "$API_BASE/instance/$instance_id/test")
            if echo "$test_response" | grep -q '"code":200'; then
                log_info "✅ 连接测试成功"
            else
                log_warn "⚠️ 连接测试失败（可能是正常的，因为是测试数据）"
            fi
        fi
    else
        log_error "❌ 创建实例失败"
        echo "响应: $response"
    fi
}

# 测试工作流API
test_workflow_api() {
    log_test "测试SQL工作流API..."
    
    # 获取工单列表
    log_info "获取SQL工单列表..."
    response=$(curl -s "$API_BASE/workflow")
    if echo "$response" | grep -q '"code":200'; then
        log_info "✅ 获取工单列表成功"
    else
        log_error "❌ 获取工单列表失败"
        echo "响应: $response"
    fi
    
    # 创建测试工单（需要先有实例）
    log_info "创建测试SQL工单..."
    workflow_data='{
        "title": "测试工单",
        "instance_id": 1,
        "db_name": "test",
        "schema_name": "public",
        "is_backup": true,
        "sql_content": "SELECT 1;",
        "method": "manual",
        "related_oid": ""
    }'
    
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$workflow_data" \
        "$API_BASE/workflow")
    
    if echo "$response" | grep -q '"code":200'; then
        log_info "✅ 创建工单成功"
    else
        log_warn "⚠️ 创建工单失败（可能需要先创建数据库实例）"
    fi
}

# 测试查询日志API
test_querylog_api() {
    log_test "测试查询日志API..."
    
    response=$(curl -s "$API_BASE/querylog")
    if echo "$response" | grep -q '"code":200'; then
        log_info "✅ 获取查询日志成功"
    else
        log_error "❌ 获取查询日志失败"
        echo "响应: $response"
    fi
}

# 测试调度器API
test_scheduler_api() {
    log_test "测试任务调度器API..."
    
    # 获取调度器状态
    log_info "获取调度器统计..."
    response=$(curl -s "$API_BASE/schedule/stats")
    if echo "$response" | grep -q '"code":200'; then
        log_info "✅ 获取调度器统计成功"
    else
        log_error "❌ 获取调度器统计失败"
        echo "响应: $response"
    fi
    
    # 获取所有任务
    log_info "获取所有任务..."
    response=$(curl -s "$API_BASE/schedule/tasks")
    if echo "$response" | grep -q '"code":200'; then
        log_info "✅ 获取任务列表成功"
    else
        log_error "❌ 获取任务列表失败"
        echo "响应: $response"
    fi
}

# 性能测试
test_performance() {
    log_test "简单性能测试..."
    
    log_info "测试健康检查接口性能..."
    time curl -s "$BASE_URL/health" > /dev/null
    
    log_info "测试实例列表接口性能..."
    time curl -s "$API_BASE/instance" > /dev/null
}

# 主函数
main() {
    log_info "=== SQL Service 测试脚本 ==="
    log_info "测试目标: $BASE_URL"
    echo ""
    
    # 检查服务状态
    if ! check_service; then
        log_error "服务未运行，请先启动服务"
        exit 1
    fi
    
    echo ""
    
    # 运行测试
    test_health
    echo ""
    
    test_swagger
    echo ""
    
    test_instance_api
    echo ""
    
    test_workflow_api
    echo ""
    
    test_querylog_api
    echo ""
    
    test_scheduler_api
    echo ""
    
    test_performance
    echo ""
    
    log_info "=== 测试完成 ==="
    log_info "如需查看详细API文档，请访问: $BASE_URL/swagger/index.html"
}

# 帮助信息
show_help() {
    cat << EOF
SQL Service 测试脚本

用法: $0 [选项]

选项:
    -u, --url URL       指定服务URL (默认: http://localhost:9983)
    -h, --help          显示此帮助信息

示例:
    $0                  # 使用默认URL测试
    $0 -u http://localhost:8080  # 使用自定义URL测试

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--url)
            BASE_URL="$2"
            API_BASE="$BASE_URL/api/sql"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main "$@"
