{"name": "devops-frontend-vue3", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@monaco-editor/loader": "^1.5.0", "@types/js-cookie": "^3.0.6", "@vueuse/core": "^13.2.0", "ace-builds": "^1.41.0", "axios": "^1.9.0", "dayjs": "^1.11.13", "element-plus": "^2.9.11", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "monaco-editor": "^0.52.2", "nprogress": "^0.2.0", "pinia": "^3.0.1", "ts-md5": "^1.3.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue3-ace-editor": "^2.2.4", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0"}, "devDependencies": {"@iconify/json": "^2.2.341", "@tsconfig/node22": "^22.0.1", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.21", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "sass": "^1.89.0", "typescript": "~5.8.0", "unplugin-auto-import": "^19.2.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.5.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}