# SQL查询页面

## 功能概述

SQL查询页面是一个完整的数据库查询工具，提供了类似于图片中展示的功能，包括：

- 数据库实例选择
- 数据库选择
- 左侧数据库结构树
- SQL编辑器
- 查询结果展示
- 执行计划查看
- 结果导出功能

## 主要功能

### 1. 数据库连接管理
- 支持多种数据库类型：MySQL、PostgreSQL、SQLite、MongoDB、Redis、Oracle、MSSQL、ClickHouse
- 实例选择下拉框，显示所有可用的数据库实例
- 数据库选择下拉框，显示选中实例下的所有数据库

### 2. 数据库结构浏览
- 左侧树形结构展示数据库实例、数据库和表
- 点击数据库节点可以展开显示表列表
- 点击表名可以自动生成SELECT语句

### 3. SQL编辑器
- 支持多行SQL语句编辑
- 提供SQL格式化功能
- 清空编辑器功能

### 4. 查询执行
- 执行SQL查询按钮，支持SELECT、INSERT、UPDATE、DELETE等语句
- 执行计划查看，帮助优化SQL性能
- 查询结果实时显示，包括影响行数和执行时间

### 5. 结果展示
- 表格形式展示查询结果
- 支持列宽调整和内容溢出提示
- 错误信息友好展示

### 6. 导出功能
- 支持查询结果导出（开发中）
- 计划支持CSV、Excel等格式

## 技术实现

### 前端技术栈
- Vue 3 + TypeScript
- Element Plus UI组件库
- Vite构建工具
- Pinia状态管理

### API接口
- 基于RESTful API设计
- 支持分页查询
- 统一错误处理
- 请求响应拦截

### 响应式设计
- 支持桌面端和移动端
- 自适应布局
- 深色模式支持

## 使用方法

1. **选择数据库实例**：在顶部工具栏选择要连接的数据库实例
2. **选择数据库**：选择实例后，选择具体的数据库
3. **浏览结构**：在左侧树形结构中浏览数据库表结构
4. **编写SQL**：在SQL编辑器中输入查询语句
5. **执行查询**：点击"执行查询"按钮运行SQL
6. **查看结果**：在下方表格中查看查询结果
7. **导出数据**：点击"导出结果"按钮下载数据（开发中）

## 快捷操作

- 点击表名：自动生成`SELECT * FROM table_name LIMIT 100;`语句
- 格式化SQL：美化SQL语句格式
- 清空结果：清除当前查询结果
- 刷新树：重新加载数据库结构树

## 安全特性

- 查询结果限制：默认限制查询结果数量，防止大量数据查询
- 权限控制：基于用户权限控制数据库访问
- SQL注入防护：后端进行SQL安全检查
- 数据脱敏：敏感数据自动脱敏显示

## 开发说明

### 文件结构
```
src/views/sql/
├── SqlQueryView.vue     # 主页面组件
└── README.md           # 说明文档

src/api/modules/
└── sql.ts              # SQL服务API接口
```

### 路由配置
```typescript
{
  path: '/sql',
  component: Layout,
  name: 'SQL',
  meta: { title: 'SQL管理', icon: 'Connection' },
  children: [
    {
      path: 'query',
      component: () => import('@/views/sql/SqlQueryView.vue'),
      name: 'SqlQuery',
      meta: { title: 'SQL查询', icon: 'Search' }
    }
  ]
}
```

### 代理配置
```typescript
// vite.config.ts
'/api/sql': {
  target: 'http://localhost:9983',
  secure: false,
  changeOrigin: true,
  rewrite: (path) => path.replace(/^\/api\/sql/, '/api')
}
```

## 后续优化

1. **代码编辑器增强**：集成Monaco Editor或CodeMirror
2. **SQL语法高亮**：支持不同数据库的SQL语法高亮
3. **查询历史**：保存和管理查询历史记录
4. **结果缓存**：缓存查询结果，提高性能
5. **批量操作**：支持批量SQL执行
6. **可视化查询**：提供可视化查询构建器
7. **性能监控**：查询性能分析和监控
8. **协作功能**：支持SQL查询分享和协作

## 注意事项

1. 确保SQL服务正常运行在端口9983
2. 数据库实例需要预先配置
3. 大量数据查询时注意性能影响
4. 生产环境使用时注意数据安全
