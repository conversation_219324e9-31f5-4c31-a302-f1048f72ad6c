# SQL服务配置文件
server:
  host: "0.0.0.0"
  port: 9986
  read_timeout: 60
  write_timeout: 60
  idle_timeout: 60

database:
  type: "postgres" # 支持: mysql, postgres, sqlite
  host: "************"
  port: 5432
  username: "devops"
  password: "ops123456"
  dbname: "ydevopsdb" # SQLite数据库文件名或MySQL/PostgreSQL数据库名
  sslmode: "disable"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600 # 连接最大生存时间(秒)

# Redis配置（用于缓存查询结果）
redis:
  host: "localhost"
  port: 6379
  password: "ops123456"
  db: 0
  pool_size: 10

# 任务调度配置
scheduler:
  enabled: true
  timezone: "Asia/Shanghai"

# SQL查询配置
sql_query:
  max_execution_time: 60 # 最大执行时间(秒)
  default_limit: 100 # 默认查询限制行数
  max_limit: 10000 # 最大查询限制行数
  disable_star: false # 是否禁用SELECT *
  data_masking: false # 是否启用数据脱敏
  query_check: false # 是否启用查询检查

# 数据脱敏配置
data_masking:
  enabled: false
  rules:
    - name: "手机号脱敏"
      regex: "(\\d{3})(\\d{4})(\\d{4})"
      replacement: "$1****$3"
    - name: "身份证脱敏"
      regex: "(\\d{6})(\\d{8})(\\d{4})"
      replacement: "$1********$3"

# 日志配置
log:
  level: "info" # 日志级别: debug, info, warn, error
  format: "text" # 日志格式: json, text
  output: "stdout" # 日志输出: stdout, stderr, file

# 用户中心服务配置
ucenter_service:
  url: "http://ucenter-service:9982"
  timeout: 5
  skip_authenticate: false

# 外部工具配置
tools:
  soar:
    path: "/usr/local/bin/soar"
    enabled: false
  sqltuning:
    enabled: true

# GoInception
goinception:
  enabled: true
  host: "************"
  port: 4000
  user: "root"
  password: "ops123456"
